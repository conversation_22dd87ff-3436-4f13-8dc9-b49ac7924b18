import * as ws_protocol from './pb/ws_protocol.js';

import { NetWork } from './NetWork';
import { SocketServerType } from './utils/Enum';
import { DataHandler } from './DataHandler';
import { NetWorkProxy } from './NetWorkProxy';
import { logging, LoginError } from 'shared';

export class WorldNetWork extends NetWorkProxy {
    private static _instance: WorldNetWork;

    private responseLoginS: any = null;
    private responseLoginSPP: boolean = false;

    private maxTimeRelogin: number = 5;
    private doLoginCount: number = 0;
    private _lastLoginRequestTime: number = 0;

    public onLogon?: (token: string) => void;
    public onLogonError?: (error: Error) => void;
    public onUserData?: (data: ws_protocol.pb.INoticeGetUserData) => void;
    public onBalance?: (balance: number) => void;

    public static getInstance(): WorldNetWork {
        if (!WorldNetWork._instance) {
            WorldNetWork._instance = new WorldNetWork();
        }
        return WorldNetWork._instance;
    }

    public init() {
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_Logon_Response, this.responseLoginServer.bind(this));
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_GetUserData_Response, this.responseGetUserData.bind(this));
        this.registerMsg(ws_protocol.pb.MSGID.MsgID_GetUserData_Notice, this.noticeGetUserData.bind(this));
    }

    public getLogonObject(forceSharedPlayer: boolean = false): object {
        const msg: ws_protocol.pb.RequestLogon = new ws_protocol.pb.RequestLogon();
        msg.version = '2.3.44';
        msg.token = forceSharedPlayer
            ? DataHandler.getInstance().getSharedPlayerToken()
            : DataHandler.getInstance().getUserToken();
        msg.device_info =
            `{"disroot":false,"dmodel":"","dname":"wefans","duuid":"${DataHandler.getInstance().getDeviceId()}","dversion":""}`;
        msg.invitation_code = '';
        msg.client_type = ws_protocol.pb.ClientType.WPTO;
        msg.CurrentLanguage = 'en_US';
        msg.os = '';
        msg.os_version = '';
        return msg;
    }

    public requestLoginServer() {
        const timeStamp = new Date().getTime();
        let isARequestOngoing = this._lastLoginRequestTime > 0;
        if (isARequestOngoing) {
            const requestTimeGap = timeStamp - this._lastLoginRequestTime;
            const threshHold = 1000;
            isARequestOngoing = requestTimeGap < threshHold;
            this._lastLoginRequestTime = 0;
        }

        if (isARequestOngoing) {
            return;
        }
        this.doLoginCount += 1;
        if (this.doLoginCount <= this.maxTimeRelogin) {
            const LoginModule = ws_protocol.pb.RequestLogon;
            if (LoginModule) {
                this.responseLoginS = null;
                this._lastLoginRequestTime = timeStamp;
                const pbbuf = LoginModule.encode(this.getLogonObject()).finish();
                NetWork.getInstance().sendMsg(
                    pbbuf,
                    ws_protocol.pb.MSGID.MsgID_Logon_Request,
                    0,
                    SocketServerType.ServerType_World_WPTO,
                    SocketServerType.ServerType_WebSocketType_WORLD
                );
                this.responseLoginSPP = false;
                // Uncomment the following lines if you want to use shared player login
                // const pbbufShared = LoginModule.encode(this.getLogonObject(true)).finish();
                //
                // NetWork.getInstance().sendMsg(
                //     pbbufShared,
                //     ws_protocol.pb.MSGID.MsgID_Logon_Request,
                //     0,
                //     SocketServerType.ServerType_World,
                //     SocketServerType.ServerType_World
                // );
            }
        } else {
            throw new Error('Login failed after multiple attempts.');
        }
    }

    public responseLoginServer(puf: any, msgid: number, serverType: number) {
        const msg: ws_protocol.pb.IResponseLogon = this.decodePB(ws_protocol.pb.ResponseLogon, puf);
        logging.withTag('WPTGO').info('responseLoginServer', msg);
        if (msg?.error && msg.error !== 1) {
            if (this.onLogonError) {
                this.onLogonError(new LoginError(msg.error.toString()));
            }
            return;
        }
        if (this.onLogon) {
            this.onLogon(msg?.mttData?.token);
        }
        if (this.onUserData || this.onBalance) {
            this.requestGetUserData();
        }
    }

    public requestGetUserData() {
        const msg: object = { user_id: DataHandler.getInstance().getUserId() };
        const pbbuf = ws_protocol.pb.RequestGetUserData.encode(msg).finish();

        return NetWork.getInstance().sendMsg(
            pbbuf,
            ws_protocol.pb.MSGID.MsgID_GetUserData_Request,
            0,
            SocketServerType.ServerType_World_WPTO,
            SocketServerType.ServerType_WebSocketType_WORLD
        );
    }

    public responseGetUserData(puf: any) {
        const msg = this.decodePB(ws_protocol.pb.ResponseGetUserData, puf);
        logging.withTag('WPTGO').info('responseGetUserData', msg);
    }

    public noticeGetUserData(puf: any) {
        const data: ws_protocol.pb.INoticeGetUserData = this.decodePB(ws_protocol.pb.NoticeGetUserData, puf);
        logging.withTag('WPTGO').info('noticeGetUserData', data);
        if (this.onUserData) {
            this.onUserData(data);
        }
        if (this.onBalance) {
            this.onBalance((data?.total_amount ?? 0) / 100); // Assuming total_amount is in cents, convert to dollars
        }
    }
}
