import { <PERSON>Handler } from './DataHandler';
import { WorldNetWork } from './WorldNetWork';
import { MD5Parser } from './md5Parser';
import { NetWork } from './NetWork';
import { MttUserData, PlatformUserData } from 'shared';
import { pb } from './pb/ws_protocol.js';

async function balance(url: string, userData: PlatformUserData, proxyUrl?: string): Promise<number> {
    return withNetwork<number>(
        url,
        userData,
        (resolve) => {
            WorldNetWork.getInstance().onBalance = resolve;
        },
        proxyUrl,
    );
}

async function mttData(url: string, userData: PlatformUserData, proxyUrl?: string): Promise<MttUserData> {
    return withNetwork<MttUserData>(
        url,
        userData,
        (resolve) => {
            let mttToken: string;
            WorldNetWork.getInstance().onLogon = (token: string) => {
                mttToken = token;
            };
            WorldNetWork.getInstance().onUserData = (userData: pb.INoticeGetUserData) => {
                const nickname = userData.nick_name;
                resolve({
                    mtt: {
                        token: mttToken
                    },
                    user: {
                        nickname: nickname
                    },
                });
            };
        },
        proxyUrl,
    );
}

async function withNetwork<T>(url:string, userData: PlatformUserData, handler: (resolve: (value: T) => void) => void, proxyUrl?: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        setUserData(userData);
        MD5Parser.md5token();

        WorldNetWork.getInstance().onLogonError = reject;
        handler(resolve);

        WorldNetWork.getInstance().init();
        NetWork.getInstance().connectServer(url, proxyUrl);
    }).finally(() => NetWork.getInstance().disconnect());
}

function hashToken(token: string): string {
    return MD5Parser.hashToken(token);
}

function setUserData(userData: PlatformUserData): void {
    const dataHandler = DataHandler.getInstance();
    dataHandler.setUserId(userData.userId);
    dataHandler.setUserToken(userData.token);
    dataHandler.setDeviceId(userData.deviceId);
}

export { balance, mttData, hashToken };
