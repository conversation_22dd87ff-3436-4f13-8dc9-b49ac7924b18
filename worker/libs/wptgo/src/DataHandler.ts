export class DataHandler {
    private static _instance: <PERSON>Handler;
    private _sharedPlayerId: number = 0;
    private _userId: number = 0;
    private _userToken: string = '';
    private _sharedPlayerToken: string = '';
    private _deviceId: string = '';

    public static getInstance(): DataHandler {
        if (!DataHandler._instance) {
            DataHandler._instance = new DataHandler();
        }
        return DataHandler._instance;
    }

    public setSharedPlayerId(playerId: number): void {
        this._sharedPlayerId = playerId;
    }

    public getSharedPlayerId(): number {
        return this._sharedPlayerId;
    }

    public setUserId(userId: number): void {
        this._userId = userId;
    }

    public getUserId(): number {
        return this._userId;
    }

    public setUserToken(userToken: string): void {
        this._userToken = userToken;
    }

    public getUserToken(): string {
        return this._userToken;
    }

    public setSharedPlayerToken(sharedPlayerToken: string): void {
        this._sharedPlayerToken = sharedPlayerToken;
    }

    public getSharedPlayerToken(): string {
        return this._sharedPlayerToken;
    }

    public setDeviceId(deviceId: string): void {
        this._deviceId = deviceId;
    }

    public getDeviceId(): string {
        return this._deviceId;
    }
}
