import { aes<PERSON><PERSON><PERSON> } from './plugg/aesHandler';
import { <PERSON>Handler } from './utils/BitHandler';
import { ByteArray } from './utils/ByteArray';
import { GameId, SocketServerType } from './utils/Enum';
import { DataHandler } from './DataHandler';
import { RoomManager } from './RoomManager';
import { AppConfig } from './AppConfig';
import { WorldNetWork } from './WorldNetWork';
import { HttpsProxyAgent } from 'https-proxy-agent';
import WebSocket from 'ws';
import { logging } from 'shared';

type MessageCallback = (
    data: any,
    msgId: number,
    serverType: number,
    serverId: number,
    roomId: number,
) => void;

export class NetWork {
    public wSocket: WebSocket | undefined = undefined; // websocket实体
    public u32seq: number = 0; // 消息序号
    public handlers = new Map<number, Map<number, MessageCallback>>(); // 所有消息的引用

    private headLen: number = 20; // 包头长度
    private static _instance: NetWork;
    private _xorValue: number = 0;

    public static getInstance(): NetWork {
        if (!NetWork._instance) {
            NetWork._instance = new NetWork();
        }
        return NetWork._instance;
    }

    //! websocket创建与连接
    public connectServer(url: string, proxyUrl?: string): void {
        console.log('[NetWork] ==> connectServer() isConnecting=' + this.isConnecting());
        if (this.isConnecting()) {
            return;
        }

        this.disconnect();

        logging.withTag('WEBSOCKET').info('WPTGO attempting to connect to websocket', {
            url,
            proxyUrl: proxyUrl || 'no proxy',
        });

        try {
            if (proxyUrl) {
                const agent = new HttpsProxyAgent(proxyUrl);
                this.wSocket = new WebSocket(url, { agent });
                logging.withTag('WEBSOCKET').info('WPTGO websocket created with proxy', {
                    url,
                    proxyUrl,
                });
            } else {
                this.wSocket = new WebSocket(url);
                logging.withTag('WEBSOCKET').info('WPTGO websocket created without proxy', {
                    url,
                });
            }
            this.initWebSocket();
        } catch (error) {
            logging.withTag('WEBSOCKET').error('WPTGO failed to create WebSocket', {
                error,
                url,
                proxyUrl: proxyUrl || 'no proxy',
            });
        }
    }

    private initWebSocket() {
        if (!this.wSocket) return;

        this.wSocket.binaryType = 'arraybuffer';
        this.u32seq = 0;
        this.wSocket.onmessage = this.onmessage.bind(this) as (event: WebSocket.MessageEvent) => void;
        let connectionInitTimeout = setInterval(() => {
            throw new Error('Unable to open websocket connection within the timeout');
        }, 5000); // 5 seconds timeout
        this.wSocket.onopen = () => {
            clearInterval(connectionInitTimeout);
            logging.withTag('WEBSOCKET').info('WPTGO websocket connection opened', {
                url: this.wSocket?.url,
            });
            WorldNetWork.getInstance().requestLoginServer();
        };

        this.wSocket.onerror = (event) => {
            logging.withTag('WEBSOCKET').error(`WPTGO websocket error, finishing job...`, {
                error: event?.error,
                message: event?.message,
                type: event?.type,
            });

            throw new Error(`WebSocket error: ${event?.error}`);
        };

        this.wSocket.onclose = (event) => {
            clearInterval(connectionInitTimeout);
            logging.withTag('WEBSOCKET').info('WPTGO websocket connection closed', {
                code: event?.code,
                reason: event?.reason,
                wasClean: event?.wasClean,
                url: this.wSocket?.url,
            });
            console.log('WebSocket instance closed.');
        };
    }

    //! 消息注册
    public registerMsg(serverId: number, msgid: number, fn: MessageCallback): void {
        let value = this.handlers.get(serverId);
        if (!value) {
            value = new Map<number, MessageCallback>();
            this.handlers.set(serverId, value);
        }
        value.set(msgid, fn);

        console.log('register:msgid: ' + msgid);
    }

    /**
     * 清理GameId的所有消息注册
     * @param serverId 对应的GameId
     */
    public unregisterMsgForGame(serverId: number) {
        const value = this.handlers.get(serverId);
        if (value) {
            value.clear();
            this.handlers.delete(serverId);
        }
    }

    //! 消息发送 loading显示
    public sendMsg(pbbuf: any, msgid: number, Roomid: number, ServerType: number, ServerId: number): boolean {
        if (this.sendPackage(pbbuf, msgid, Roomid, ServerType, ServerId)) {
            return true;
        }
        console.log(ServerId + ': ' + msgid + ' FAILED!');
        return false;
    }

    /**
     * 消息封包
     * @param pbbuf 数据
     * @param msgid 消息ID
     * @param Roomid 房间ID
     * @param ServerType 服务器类型 SeverType_World, SeverType_Game
     * @param ServerId 游戏服ID
     */
    public sendPackage(
        pbbuf: ArrayBuffer | Uint8Array | null,
        msgid: number,
        Roomid: number,
        ServerType: number,
        ServerId: number,
    ): boolean {
        let entryptStr: Uint8Array | undefined;
        const isEncrypt: boolean = this.isEncrypt(ServerId);
        if (isEncrypt && pbbuf) {
            const arrayBuffer = pbbuf instanceof Uint8Array ? pbbuf : new Uint8Array(pbbuf);
            entryptStr = aesHandler.EncryptBytes(arrayBuffer);
        }
        let contentLength = 0;
        if (isEncrypt && entryptStr) {
            contentLength = entryptStr.length;
        } else if (pbbuf) {
            contentLength = pbbuf.byteLength;
        }
        const u16PackLen = this.headLen + contentLength; // 消息总长度
        const u16Msgid = msgid; // 消息ID
        let u32playerid = DataHandler.getInstance().getSharedPlayerId();
        const u32roomid = Roomid; // 房间ID
        const u16serverType = ServerType; // 服务器类型
        const u16serverId = ServerId; // 服务器ID

        if (ServerType === SocketServerType.ServerType_World_WPTO) {
            // make sure correct ID is used
            u32playerid = DataHandler.getInstance().getUserId();
        }

        const burffer = new ByteArray();
        burffer.createBuffer(new ArrayBuffer(1024));
        burffer.writeUint16(u16serverType);
        burffer.writeUint16(u16serverId);
        burffer.writeUint16(u16PackLen);
        burffer.writeUint16(u16Msgid);
        burffer.writeUint32(this.u32seq);
        burffer.writeUint32(Number(u32playerid));
        burffer.writeUint32(u32roomid);

        burffer.writeBuffer(isEncrypt && entryptStr ? entryptStr : pbbuf!);
        burffer.wpos = u16PackLen;
        console.log(
            'send data:====>> u16Msgid:' +
                u16Msgid +
                ' u16serverType:' +
                u16serverType +
                ' u16serverId:' +
                u16serverId +
                ' u16PackLen:' +
                u16PackLen +
                ' U32seq:' +
                this.u32seq +
                ' U32playerid:' +
                u32playerid +
                ' U32roomid:' +
                u32roomid,
        );
        return this.send(burffer.getbuffer());
    }

    //! 消息发送
    public send(data: ArrayBuffer): boolean {
        if (!this.wSocket) return false;
        console.log('=====> wSocket.readyState  1111= ' + this.wSocket.readyState);
        if (this.wSocket.readyState === WebSocket.OPEN) {
            console.log('open send data');
            this.wSocket.send(data);
            this.u32seq += 1;
            return true;
        }
        return false;
    }

    private getValueByOp(opType: number, bitSize: number, value: number): number {
        const _xorValue = this._xorValue;
        if (opType === 0) {
            if (bitSize === 8) {
                return (value ^ _xorValue) & 0xff;
            }
            if (bitSize === 16) {
                return (value ^ _xorValue) & 0xffff;
            }
            if (bitSize === 32) {
                return value ^ _xorValue;
            }
        }
        if (opType === 1) {
            // 数据位翻转
            return BitHandler.reverse_bits(value, bitSize);
        }
        if (opType === 2) {
            // 数据位相邻两位互换
            if (bitSize === 8) {
                return BitHandler.swapoddeven_8bits(value);
            }
            if (bitSize === 16) {
                return BitHandler.swapoddeven_16bits(value);
            }
            if (bitSize === 32) {
                return BitHandler.swapoddeven_32bits(value);
            }
        }
        if (opType === 3) {
            // 对数据进行取反
            if (bitSize === 8) {
                return ~value & 0xff;
            }
            if (bitSize === 16) {
                return ~value & 0xffff;
            }
            if (bitSize === 32) {
                return ~value;
            }
        }
        return 0;
    }

    private parsePolicyData(buffer: ByteArray, policyData1: number, policyData2: number): number[] {
        const bitExtent = 32;
        let offset = 0;

        const retArray: number[] = new Array(7).fill(0); // Pre-allocate array with 7 elements
        const _MsgHeadMap = new Map<number, number>();

        let _curPlicyData = policyData1;

        const msgHeaderFlag = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, 8);
        offset += 8;
        if (msgHeaderFlag !== 0x8c && msgHeaderFlag !== 0x7a) {
            console.log('Error: parsePolicyData error. unknow msgHeaderFlag:' + msgHeaderFlag);
            return retArray;
        }

        if (msgHeaderFlag === 0x7a) {
            retArray[0] = buffer.readUint16(); // U32serverType
            retArray[1] = buffer.readUint16(); // U32serverid
            retArray[2] = buffer.readUint16(); // u16PackLen
            retArray[3] = buffer.readUint16(); // u16Msgid
            retArray[4] = buffer.readUint32(); // u32seq
            retArray[5] = buffer.readUint32(); // U32playerid
            retArray[6] = buffer.readUint32(); // U32roomid
            return retArray;
        }

        const msgBitLen = BitHandler.getReadMidNumFromByte(_curPlicyData, bitExtent, offset, offset + 4);
        offset += 4;

        for (let i = 0; i < 7; i++) {
            let msgType = 0;
            let msgValue = 0;

            if (bitExtent - offset < msgBitLen) {
                const _remainBit = BitHandler.readRightBitFromByte(
                    _curPlicyData,
                    bitExtent,
                    bitExtent - offset,
                );
                _curPlicyData = policyData2;
                const _feedBitLen = msgBitLen - (bitExtent - offset);
                const _feedBit = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, _feedBitLen);
                msgType = BitHandler.concatBinaryNumber(_remainBit, _feedBit, _feedBitLen);
                offset = _feedBitLen;
            } else {
                msgType = BitHandler.getReadMidNumFromByte(
                    _curPlicyData,
                    bitExtent,
                    offset,
                    offset + msgBitLen,
                );
                offset += msgBitLen;
            }

            if (bitExtent - offset < 2) {
                const _remainBit = BitHandler.readRightBitFromByte(
                    _curPlicyData,
                    bitExtent,
                    bitExtent - offset,
                );
                _curPlicyData = policyData2;
                const _feedBitLen = 2 - (bitExtent - offset);
                const _feedBit = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, _feedBitLen);
                msgValue = BitHandler.concatBinaryNumber(_remainBit, _feedBit, _feedBitLen);
                offset = _feedBitLen;
            } else {
                msgValue = BitHandler.getReadMidNumFromByte(_curPlicyData, bitExtent, offset, offset + 2);
                offset += 2;
            }

            if (offset >= bitExtent) {
                _curPlicyData = policyData2;
                offset = 0;
            }

            _MsgHeadMap.set(msgType, msgValue);
        }

        // Skip invalid bytes
        const slackByteLen = BitHandler.readRightBitFromByte(_curPlicyData, bitExtent, 3);
        for (let i = 0; i < slackByteLen; i++) {
            buffer.readUint8();
        }

        // Parse message fields based on operation type
        _MsgHeadMap.forEach((opValue: number, key: number) => {
            let value: number;
            switch (key) {
                case 0: // U32serverType
                    value = buffer.readUint16();
                    retArray[0] = this.getValueByOp(opValue, 16, value);
                    break;
                case 1: // U32serverid
                    value = buffer.readUint16();
                    retArray[1] = this.getValueByOp(opValue, 16, value);
                    break;
                case 2: // u16PackLen
                    value = buffer.readUint16();
                    retArray[2] = this.getValueByOp(opValue, 16, value);
                    break;
                case 3: // u16Msgid
                    value = buffer.readUint16();
                    retArray[3] = this.getValueByOp(opValue, 16, value);
                    break;
                case 4: // u32seq
                    value = buffer.readUint32();
                    retArray[4] = this.getValueByOp(opValue, 32, value);
                    break;
                case 5: // U32playerid
                    value = buffer.readUint32();
                    retArray[5] = this.getValueByOp(opValue, 32, value);
                    break;
                case 6: // U32roomid
                    value = buffer.readUint32();
                    retArray[6] = this.getValueByOp(opValue, 32, value);
                    break;
                default:
                    console.error('Unhandled message key:', key);
                    break;
            }
        });

        return retArray;
    }

    private transformServerId(serverId: number): number {
        if (RoomManager.getInstance().checkGameIsZoom(serverId)) {
            return GameId.Texas;
        }

        if (
            serverId === GameId.Bet ||
            serverId === GameId.StarSeat ||
            serverId === GameId.Plo ||
            serverId === GameId.Plo5
        ) {
            return GameId.Texas;
        }

        if (serverId === GameId.World_WPTO) {
            return GameId.World;
        }

        return serverId;
    }

    private processMessage(
        pbbuf: ArrayBuffer,
        msgId: number,
        serverType: number,
        serverId: number,
        roomId: number,
        originalServerId: number,
    ): void {
        let decryptStr: Uint8Array | undefined;
        const isEncrypt: boolean = this.isEncrypt(serverId);

        if (isEncrypt) {
            decryptStr = aesHandler.DecryptBytes(new Uint8Array(pbbuf));
        }

        const transformedServerId = this.transformServerId(serverId);
        const value = this.handlers.get(transformedServerId);

        if (value) {
            const func = value.get(msgId);
            if (typeof func === 'function') {
                if (AppConfig.isProd === false) {
                    func(
                        isEncrypt && decryptStr ? decryptStr : new Uint8Array(pbbuf),
                        msgId,
                        serverType,
                        originalServerId,
                        roomId,
                    );
                } else {
                    try {
                        func(
                            isEncrypt && decryptStr ? decryptStr : new Uint8Array(pbbuf),
                            msgId,
                            serverType,
                            originalServerId,
                            roomId,
                        );
                    } catch (e) {
                        console.error('onmessage ' + msgId + ' err:' + e);
                    }
                }
            } else {
                console.log('unregistered message id = ' + msgId);
            }
        } else {
            console.log('unregistered game message id = ' + transformedServerId);
        }
    }

    public onmessage(msg: { data: ArrayBuffer }): void {
        const burffer = new ByteArray();
        burffer.createBuffer(msg.data);
        burffer.wpos = msg.data.byteLength;

        const policyData1 = burffer.readUint32();
        const policyData2 = burffer.readUint32();
        this._xorValue = policyData2;
        const retHeaderArray: number[] = this.parsePolicyData(burffer, policyData1, policyData2);
        if (retHeaderArray.length < 1) {
            console.log('Error: onmessage retHeaderArray is null.');
            return;
        }
        const serverType = retHeaderArray[0];
        const serverId = retHeaderArray[1];
        const packLen = retHeaderArray[2];
        const msgId = retHeaderArray[3];
        const seq = retHeaderArray[4];
        const playerId = retHeaderArray[5];
        const roomId = retHeaderArray[6];
        const originalServerId = serverId;

        console.log(
            'received:  u16Msgid:' +
                msgId +
                '  U32serverid:' +
                serverId +
                '  U32serverType:' +
                serverType +
                '  u16PackLen:' +
                packLen +
                '  u32seq:' +
                seq +
                '  U32playerid:' +
                playerId +
                '  U32roomid:' +
                roomId,
        );

        const pbbuf = burffer.getbuffer();
        this.processMessage(pbbuf, msgId, serverType, serverId, roomId, originalServerId);
    }

    //! 主动断开，不走重连逻辑
    disconnect(): void {
        console.log('NetWork: disconnect close');
        this.close();
    }

    //! 关闭 websocket, 触发重连
    close(): void {
        console.log('NetWork:close');
        const ws = this.wSocket;
        if (ws) {
            ws.close();
            ws.onopen = null;
            ws.onmessage = null;
            ws.onerror = null;
            ws.onclose = null;
            this.wSocket = undefined;
            this.u32seq = 0;
        }
    }

    //! websocket连接状态
    isConnect(): boolean {
        return this.wSocket !== null && this.wSocket?.readyState === WebSocket.OPEN;
    }

    //! 是否在连接
    isConnecting(): boolean {
        return this.wSocket !== null && this.wSocket?.readyState === WebSocket.CONNECTING;
    }

    isEncrypt(serverId: number): boolean {
        return false;
    }
}
