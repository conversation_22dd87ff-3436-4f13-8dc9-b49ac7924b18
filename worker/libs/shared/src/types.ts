import { GameMode, JobType } from './constants';

export interface AdapterContext {
    jobId?: string;
    platform?: string;
    jobType?: JobType;
    playerId?: string;
    version?: string;
}

export interface GameStateInterface {
    gameuuid: string;
    game_type: string;
    game_mode_code: GameMode;
    roomid: string;
    big_blind: number;
    ante: number;
    actions: GameActions;
    players: GamePlayer[];
    dealer_seat: number;
    sb_seat: number;
    bb_seat: number;
    straddle_seat?: number;
}

export interface GameActions {
    entries: GameAction[];
}

export interface GameAction {
    action: StrategyResponseAction | string; // 'check', 'call', 'bet', 'fold', 'raise', 'allin' or card string like 'QcKc3c';
    amount?: number;
    probability?: number;
    seat_no?: number;
    pot_size?: number;
}

export interface GamePlayer {
    uid?: string;
    seat_no: number;
    stack: number;
    hole_cards?: string;
}

export interface StrategyProfile {
    target_profile_name?: string;
}

export interface BetProfile {
    profile_name?: string;
    current_user_id?: string;
}

export interface StrategyAction {
    hand_strategy: number | null;
    action: {
        action: StrategyResponseAction;
        amount?: number;
        pot_size?: number;
    };
    action_name: string;
}

export interface StrategyResponse {
    strategy: {
        actions: StrategyAction[];
    };
    solution_information?: {
        source: string;
        strategy_profile_config: {
            [key: string]: any;
        };
        bet_profile_config: {
            [key: string]: any;
        };
    };
}

export interface FetchStrategyArgs {
    state: GameStateInterface;
    mtt?: any;
    service?: string;
    betProfileUserId?: string;
    profileName?: string;
    hints?: any[],
    exploiting_info?: Record<string, any>;
}

export interface MttUserData {
    mtt: {
        token: string;
    };
    user: {
        nickname: string;
    };
}

export enum StrategyResponseAction {
    CHECK = 'check',
    CALL = 'call',
    BET = 'bet',
    FOLD = 'fold',
    RAISE = 'raise',
    ALLIN = 'allin',
}

export type FetchStrategyFunc = (
    state: GameStateInterface,
    mtt?: any,
    service?: string,
) => Promise<GameAction | null>;

export class LoginError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'LoginError';
        Object.setPrototypeOf(this, LoginError.prototype);
    }
}

export type RoomStartParams = {
    buyInMultiplier: number;
    rebuyEnabled: boolean;
    rebuyThreshold: number;
    maxRebuyCount: number;
    withdrawAmount: number;
    withdrawThreshold: number;
    profileName: string | undefined;
    extraTimeEnabled?: boolean;
};

export enum UserStatus {
    default = 'initialized',
    loggedOn = 'logged_in',
    inLobby = 'in_lobby',
    inRoom = 'in_room',
    satDown = 'sat_down',
    satOut = 'sat_out',
    boughtIn = 'bought_in',
    inGamePlay = 'playing',
    waiting = 'waiting',
    stuck = 'stuck',
    viewingTimeExpired = 'viewing_time_expired',
    loggedOnJoinRoom = 'logged_in_joined_room',
}

export enum PlayerStatus {
    INITIALIZED = 'initialized',
    ERROR = 'error',
    STOPPING = 'stopping',
    STOPPED = 'stopped',
}

export type JobUpdateData = {
    /**
     * Data type that can be sent from Worker back to Manager
     */
    status?: PlayerStatus | UserStatus;
    stats?: {
        [key: string]: any;
    };
    balance?: Balance;
    registered?: boolean;
    tables?: any[];
    tournaments?: any[];
    club_id?: number;
};

export type JobDataHandler = (data: JobUpdateData) => void;

export interface Balance {
    diamond: number;
    gold: number;
    usdt: number;
    usd: number;
}

export type MttConfigType = {
    mttWorld: string;
    mttGame: string;
    mttApi: string;
};

export enum AppType {
    FRIENDS = 69,
    CASH = 81,
    DIAMOND = 82,
    MTT = 83,
    R4 = 84,
    SPLASH = 85,
    SPLASH_DIAMOND = 86,
    ZOOM = 87,
    SHORTDECK = 89,
}

export enum PlatformType {
    RWPK = 94,
    WPK = 98,
    WPTGO = 101,
}

export interface WorkerData {
    id: string;
    name: JobType;
    data: JobData;
}

export interface UserData {
    platformId: PlatformType;
    userId: number;
    username: string;
    countryCode?: string;
    areaCode?: string;
    phoneNum?: string;
    email?: string;
    password: string;
    deviceId: string;
}

export interface JobData extends UserData {
    /**
     * Data that is passed from Manager to Redis
     */
    appId: AppType;
    appIds?: AppType[];
    playerId?: string;
    tableId?: number;
    clubId?: number;
    buyInMultiplier?: number; // 100, 200, 300
    rebuyEnabled?: boolean;
    rebuyThreshold?: number; // 0-100 percentage of the calculated buy-in
    maxRebuyCount?: number;
    withdrawAmount?: number;
    withdrawThreshold?: number;
    ticketId?: string;
    proxyUrl?: string;
    profileName?: string;
    createRoomParams?: any; // Type is handled in `friends`
}

export interface PlatformUserData {
    userId: number;
    token: string;
    sharedPlayerId?: number;
    sharedPlayerToken?: string;
    deviceId: string;
}

export interface PkwAuthData {
    appIP: string;
    token: string;
    uid: number;
    gate_addr: string[];
    pkw_file_addr: string;
    web_server: string;
    client_type?: number;
}

export interface PkwUserData extends PlatformUserData {
    pkwAuthData: PkwAuthData;
    user: {
        nickname?: string;
    };
}

export interface TableData {
    tableId: number,
    gameType: string,
    gameId: number,
    gameMode: number,
    roomMode: number,
    currency: string,
    blinds: number[],
    straddle: boolean,
    ante: number,
    playersCount: number,
    maxPlayers: number,
    leftSeats: number,
    tableName: string,
    appId?: number
}

export type PlayerStats = {
    handsPlayed: number;
    totalBuyIn: number;
    lastBuyIn: number;
    rebuyCount: number;
    stack: number;
};
