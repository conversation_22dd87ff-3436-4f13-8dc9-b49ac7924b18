{"name": "rmtt", "version": "1.0.0", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "dependencies": {"axios": "^1.9.0", "bullmq": "^5.51.1", "busboy": "^1.6.0", "express": "^5.1.0", "protobufjs": "^6.11.4", "shared": "file:../shared", "websocket": "^1.0.35", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@types/node": "^22.15.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "scripts": {"start": "node src/main.js", "build": "tsc && npm run cp_other_files", "cp_other_files": "cpx 'src/**/*.{js,d.ts,proto,json,csv}' dist", "forever": "forever start src/main.js", "test": "tsx --test"}, "author": "", "license": "ISC"}