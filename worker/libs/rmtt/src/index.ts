import { UnrecoverableError } from 'bullmq';
import { scanAndFetch } from './scanning';
import { JobDataHandler } from 'shared';

const { HttpApis } = require('./httpApis.js');
const { signupAndPlay } = require('./mtt.js');
const MttConfig = require('./config.js');

class MttMain {
    private storeTournamentDetails: Function;
    private fetchTournamentDetails: Function;

    static init(storeTournamentDetails: Function, fetchTournamentDetails: Function) {
        mtt.storeTournamentDetails = storeTournamentDetails;
        mtt.fetchTournamentDetails = fetchTournamentDetails;
    }

    static async run(
        token: string,
        action: string,
        tournamentId: number,
        ticketId: number,
        onMessage: JobDataHandler,
        nickname: string,
    ): Promise<void> {
        switch (action) {
            case 'scan':
                return mtt.scan(token, onMessage);
            case 'play':
                return mtt.play(token, tournamentId, ticketId, onMessage);
            case 'check':
                return mtt.check(token, tournamentId, nickname, onMessage);
            default:
                throw new UnrecoverableError(`Unknown action: ${action}`);
        }
    }

    async scan(token: string, onMessage: JobDataHandler): Promise<void> {
        return scanAndFetch(token, onMessage, this.storeTournamentDetails);
    }

    async play(token: string, tournamentId: number, ticketId: number, onMessage: JobDataHandler) {
        return signupAndPlay(token, tournamentId, ticketId, onMessage, this.fetchTournamentDetails);
    }

    async check(token: string, tournamentId: number, nickname: string, onMessage: JobDataHandler): Promise<void> {
        return new Promise((resolve, reject) => {
            const httpApis = new HttpApis();

            httpApis.requestMttTournamentPlayers(
                token,
                tournamentId,
                nickname,
                (data: any) => {
                    if (data.ErrorCode) {
                        reject(data.ErrorCode);
                    } else {
                        onMessage({ registered: data.PlayersDetail?.length > 0 });
                        resolve();
                    }
                },
                (error: any) => {
                    reject(error);
                },
            );
        });
    }
}

const mtt = new MttMain();

export { MttMain, MttConfig };
