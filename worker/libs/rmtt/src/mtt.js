const CONNECTION_STATUS = require('./connection-status');
const { HttpApis } = require('./httpApis');
const { WorldPlayer, GamePlayer } = require('./player');
const WEBSOCKET_EVENT_ID = require('./websocket-event-id');
const commonProto = require('./mtt/pb/commonProto.js').commonProto;
const holdem = require('./mtt/pb/holdem.js').holdem;
const mttPro = require('./mtt/pb/mtt.js').mttPro;

const { calculateDelayMs, fetchStrategy, logging } = require('shared');

const sleep = (duration) => new Promise((resolve) => setTimeout(resolve, duration));

async function signupAndPlay(token, mttId, ticketId, updateProgress, fetchTournamentDetails) {
    logging.setTournamentId(mttId);
    return new Promise((resolve, reject) => {
        runMtt(token, mttId, ticketId, updateProgress, fetchTournamentDetails, reject, resolve);
    });
}

function runMtt(token, mttId, ticketId, updateProgress, fetchTournamentDetails, reject, resolve) {
    const onError = (error) => {
        reject(error);
        cleanup();
    };
    const finish = () => {
        resolve();
        cleanup();
    };

    let httpApis = new HttpApis();

    let getMultipleTableTimeout = null;
    let tournamentStatusTimeout = null;

    let i = 0;
    let dataManager = { token: token };

    let gamePlayer = new GamePlayer(i, dataManager, mttId);
    let worldPlayer = new WorldPlayer(i, dataManager, null, gamePlayer, mttId, ticketId);
    let myJoinStatus = 0;

    let roomId = 0;
    let seatNum = -1;
    let userId = -1;

    let tournamentDetail = null;
    let payoutStructure = [];
    let tournamentPrizePool = 0;
    let tournamentPlayers = [];
    let tournamentPlayersSize = 0;

    let state = {
        gameuuid: '',
        game_type: 'nlhe',
        game_mode_code: 'normal',
        roomid: mttId,
        big_blind: 0,
        ante: 0,
        actions: { entries: [] },
        players: [],
        dealer_seat: -1,
        sb_seat: -1,
        bb_seat: -1,
        straddle_seat: -1, // straddle seat is not taken into account for most of the logic
    };
    let pot = 0;
    let riseIndex = 1;
    let currentLevel = 1;

    // Cards in your hand
    let holeCards = [];

    // Common cards on the board
    let boardCards = [];

    // Players that were forced to all-in
    let allinedPlayers = new Set([]);

    let stats = null;

    const maxConnectAttempts = 10;
    let connectAttempt = 0;

    let onConnected = () => {
        logging.info('WorldPlayer Connected');
        worldPlayer.setConnectionStatus(CONNECTION_STATUS.CONNECTED);
        // feature: login / authentication
        worldPlayer.requestUserLogin();

        // feature: heartbeat, not neccessary
        worldPlayer.websocket.keepPing();
    };

    let requestMttMultiTable = (userId) => () => {
        logging.info('requestMttMultiTable req', {userId});
        httpApis.requestMttMultiTable(dataManager.token, userId, (data) => {
            logging.info('requestMttMultiTable res', data);
            for (let d = 0; d < data.UserGameInfo.length; d++) {
                if (mttId === data.UserGameInfo[d].SngMttLevelId) {
                    clearInterval(getMultipleTableTimeout);
                    gamePlayer.connect();
                }
            }
        });
    };

    let onUserLoginResponse = (msg) => {
        logging.info(`onUserLoginResponse: ${msg}`);
        worldPlayer.setError('');
        if (worldPlayer.websocket) {
            worldPlayer.dataManager.userData = msg.UserData;
            worldPlayer.dataManager.token = msg.Token;
            worldPlayer.websocket.hasVerifyToken = true;
            worldPlayer.setConnectionStatus(CONNECTION_STATUS.LOGINED);
            userId = msg.UserData.Id;
            logging.setUserId(userId);
        }

        // feature: get table joined status
        myJoinStatus = worldPlayer.getMyJoinStatus(msg.JoinedTournaments, mttId);
        worldPlayer.setTournamentJoinStatus(myJoinStatus);

        // feature: get table data
        updateTournamentDetails()
            .then(() => {                
                clearInterval(getMultipleTableTimeout);
                requestMttMultiTable(worldPlayer.dataManager?.userData?.Id)();
                getMultipleTableTimeout = setInterval(
                    requestMttMultiTable(worldPlayer.dataManager?.userData?.Id),
                    10000,
                );

                signup(onError);
            })
            .catch(onError);
    };

    const updateTournamentDetails = async (isRefresh = false) => {
        logging.info(`updateTournamentDetails: ${mttId} isRefresh: ${isRefresh}`);
        // feature: get table data
        let onErrorFunc = (error) => {
            logging.error('Skipping error: requestMttTournamentDetail error', error);
        };
        if (!isRefresh) {
            onErrorFunc = onError;
        }

        const processTournamentDetails = (data) => {
            if (data.TournamentDetail && data.TournamentDetail.PrizeMoney) {
                payoutStructure = data.TournamentDetail.PrizeMoney.map((p) => ({
                    position: p.Rank,
                    prize_value: parseInt(p.Money),
                })).filter((p) => p.prize_value > 0);
                tournamentPrizePool = payoutStructure.reduce((acc, p) => acc + p.prize_value, 0);
            } else {
                logging.warn('PrizeMoney not found in tournament details', data);
            }

            if (tournamentPlayers.length === 0) {
                tournamentPlayers = (data.TournamentDetail.PlayersDetail || []).map((player) => ({
                    stack: player.Coins,
                    rank: player.Rank || player.Index,
                }));
                tournamentPlayersSize = (data.TournamentDetail.PlayersDetail || []).length;

                const player = (data.TournamentDetail.PlayersDetail || []).find((player) => player.UserId === userId);
                if (player) {
                    stats = {
                        chips: player.Coins,
                        rank: player.Rank || player.Index,
                    };
                }
            }
            tournamentDetail = data;
        };

        const detailRequest = async () => {
            return new Promise((resolve, reject) => {
                httpApis.requestMttTournamentDetail(dataManager.token, mttId, resolve, reject);
            });
        };

        try {
            let data = await fetchTournamentDetails(mttId);
            if (data != null) {
                logging.info(`Fetch tournament details successful processing: ${JSON.stringify(data)}`);
            } else if (!isRefresh) {
                logging.info(`Fetch tournament details failed, making a request`);
                data = await detailRequest();
            }
            processTournamentDetails(data);
        } catch (error) {
            onErrorFunc(error);
        }
    };

    let signup = () => {
        logging.info(`signup: ${mttId}`);
        if (!myJoinStatus) {
            worldPlayer.signup(onError);
        } else {
            worldPlayer.getTournamentStatusInterval();
        }

        tournamentStatusTimeout = setInterval(() => {
            if (worldPlayer.tournamentStatus === commonProto.MTT_GAME_STATUS.NOT_STARTED) {
                updateProgress({ stats: stats, status: 'waiting' });
            } else if (worldPlayer.tournamentStatus < commonProto.MTT_GAME_STATUS.ENDED) {
                updateProgress({ stats: stats, status: 'playing' });
            } else {
                finish();
            }
        }, 5000);
    };

    let cleanup = () => {
        clearInterval(getMultipleTableTimeout);
        clearInterval(tournamentStatusTimeout);
        worldPlayer.clearTournamentStatusInterval();
    };

    const onBroadcastMessageEnvelope = (msg) => {
        if (msg.TypeId === 1002) {
            let msgOutGame = commonProto.Broadcast_Message_Out_Game.decode(msg.Body);
            if (mttId === msgOutGame.TournamentId) {
                logging.info('Broadcast_Message_Out_Game', msgOutGame);
            }
        } else if (msg.TypeId === 1004) {
            let msgEndGame = commonProto.Broadcast_Message_End_Game.decode(msg.Body);
            if (mttId === msgEndGame.TournamentId) {
                logging.info('Broadcast_Message_End_Game', msgEndGame);
            }
        }
    };

    const onUserTokenRes = (_) => {
        gamePlayer.MTTEnterRoom({ mttId: mttId });
    };

    const onEnterRoomRes = (msg) => {
        if (mttId === msg.mttId) {
            roomId = msg.roomId;
            logging.setRoomId(roomId);
            gamePlayer.roomId = roomId;
            state.roomid = roomId.toString();
            state.gameuuid =
                mttId +
                ':' +
                roomId +
                ':' +
                userId +
                ':' +
                toMillis(tournamentDetail.TournamentDetail.StartingTime);
            state.big_blind = msg.bb;
            state.ante = msg.ante;
        }
    };
    const onRoomSnapshotMsg = (msg) => {
        if (mttId === msg.mttId) {
            if (msg.players) {
                let selfPlayer = msg.players.find((obj) => obj.userId === userId);
                if (selfPlayer) {
                    seatNum = selfPlayer.seatNum;
                }
                state.players = msg.players.map((player) => ({
                    seat_no: player.seatNum - 1,
                    stack: player.leftCoin,
                }));
            }
            state.bb_seat = msg.bbPos - 1;
            state.sb_seat = msg.sbPos - 1;
            state.dealer_seat = msg.dealerPos - 1;
            checkAndFixSeats();
            state.actions.entries = [];
            holeCards = Array.from(msg.holeCards).map(decodeCard);
            boardCards = Array.from(msg.boardCards).map(decodeCard);
            pot = msg.pot && msg.pot.reduce((acc, val) => acc + val, 0);

            CancelAutoPlay();
        }
    };

    const onMttRoomSnapshotRes = (msg) => {
        if (roomId === msg.roomId) {
            riseIndex = msg.blindIndex;
            currentLevel = riseIndex;
            state.big_blind =
                tournamentDetail?.TournamentDetail?.HoldemBlindsConfig[currentLevel - 1]?.BigBlind;
            state.ante = tournamentDetail?.TournamentDetail?.HoldemBlindsConfig[currentLevel - 1]?.Ante;
        }
    };

    // feature: new player take sit
    const onSeatOccupiedMsg = (msg) => {
        if (roomId === msg.roomId) {
            if (msg.userId === userId) {
                seatNum = msg.seatNum;
            }
        }
    };

    // feature: current player become auto play because long time no action
    const onAutoPlayMsg = (msg) => {
        if (roomId === msg.roomId && msg.userId === userId && msg.autoPlay === true) {
            CancelAutoPlay();
        }
    };

    const onNeedActionMsg = async (msg) => {
        // add allined player's action if any
        // this covers the case when allined player is immediately after the bb
        if (msg.roomId === roomId && allinedPlayers.size > 0) {
            let prevSeat = (msg.seatNum - 2 + state.players.length) % state.players.length;
            if (allinedPlayers.has(prevSeat)) {
                // if allined player is bb, their action should be after other players
                if (prevSeat !== state.bb_seat || state.actions.entries.length !== 0) {
                    logging.info('adding allin action for seat', {prevSeat});
                    state.actions.entries.push({ action: 'allin' });
                    allinedPlayers.delete(prevSeat);
                }
            }
        }
        if (msg.roomId === roomId && msg.seatNum === seatNum) {
            let stopWatch = new Date().getTime();
            let action =
                msg.optAction % 10 === holdem.Action.CHECK ? holdem.Action.CHECK : holdem.Action.FOLD;
            let amount;
            let mtt = mttData();

            try {
                let strategy = await fetchStrategy(state, mtt);
                switch (strategy.action) {
                    case 'check':
                        action = holdem.Action.CHECK;
                        break;
                    case 'call':
                        action = holdem.Action.CALL;
                        break;
                    case 'bet':
                        action = holdem.Action.BET;
                        amount = strategy.amount;
                        if (amount < msg.minBetCoin) {
                            amount = msg.minBetCoin;
                        }
                        break;
                    case 'fold':
                        action = holdem.Action.FOLD;
                        break;
                    case 'raise':
                        action = holdem.Action.RAISE;
                        amount = strategy.amount;
                        if (amount < msg.minBetCoin) {
                            amount = msg.minBetCoin;
                        }
                        break;
                    case 'allin':
                        action = holdem.Action.ALL_IN;
                        break;
                    default:
                        logging.warn('unsupported strategy action', strategy);
                        return;
                }

                if (
                    (action === holdem.Action.CALL || action === holdem.Action.RAISE) &&
                    msg.optAction === holdem.Action.ALL_IN
                ) {
                    action = holdem.Action.ALL_IN;
                }
            } catch (error) {
                logging.error('fetchStrategy error', error, { state, mtt });
            }

            // game stage is of type GAME_PHASE which has value
            // according to the length of board cards
            // so we can assign it directly
            const gameStage = boardCards.length;

            const calculatedDelayMs = calculateDelayMs(gameStage, action);
            const timePassed = new Date().getTime() - stopWatch;
            const delayLeftMs = calculatedDelayMs - timePassed;
            logging
                .withTag('DELAY')
                .info(
                    `Calculated delay: ${calculatedDelayMs} (ms) for ${holdem.Action[action]} on ${gameStage}, delay left: ${delayLeftMs} ms`,
                );

            if (delayLeftMs > 0) {
                await sleep(delayLeftMs);
            }

            gamePlayer.Action(action, amount);
        }
    };

    const mttData = () => {
        let blind = tournamentDetail.TournamentDetail.HoldemBlindsConfig[currentLevel - 1];

        // check if tournament players contains current room players
        // if not add them with artificial ranks
        let playersStacks = state.players.map((player) => player.stack).sort((a, b) => b - a);

        // remove from stacks players that are already in tournament players
        let i = 0,
            j = 0;
        while (i < tournamentPlayers.length && j < playersStacks.length) {
            if (tournamentPlayers[i].stack === playersStacks[j]) {
                playersStacks.splice(j, 1);
                i++;
            } else if (tournamentPlayers[i].stack > playersStacks[j]) {
                i++;
            } else {
                j++;
            }
        }

        // create artificial players with stacks that are not in tournament players
        let artificialPlayers = playersStacks.map((stack, i) => ({
            stack: stack,
            rank: tournamentPlayers.length + i + 1,
        }));

        // sort concatenated players by stack and set ranks accordingly
        let players = tournamentPlayers
            .concat(artificialPlayers)
            .sort((a, b) => b.stack - a.stack)
            .map((player, i) => ({
                stack: player.stack,
                rank: i + 1,
            }))
            .filter((player) => player.stack != null && player.stack > 0);

        let bounty;
        if (tournamentDetail.TournamentDetail.TournamentMode === commonProto.TOURNAMENT_MODE.HUNTER) {
            bounty = {
                initial_bounty: tournamentDetail.TournamentDetail.BountyFee,
                bounty_type: 'normal_knockout_ko'
            }
        }

        return {
            prize_pool: tournamentPrizePool,
            payout_structure: payoutStructure,
            total_registered_players:
                tournamentPlayersSize > players.length ? tournamentPlayersSize : players.length,
            players: players,
            current_level: currentLevel,
            current_blind_level: currentLevel,
            blind_structure: [
                {
                    level: blind.Level,
                    ante: blind.Ante,
                    small_blind: blind.SmallBlind,
                    big_blind: blind.BigBlind,
                },
            ],
            bounty_structure: bounty,
        };
    };

    const onPlayerActionMsg = (msg) => {
        if (roomId === msg.roomId) {
            let action;
            let amount;
            switch (msg.action) {
                case holdem.Action.CHECK:
                    action = 'check';
                    break;
                case holdem.Action.CALL:
                    action = 'call';
                    break;
                case holdem.Action.BET:
                    action = 'bet';
                    amount = msg.deskCoin;
                    break;
                case holdem.Action.FOLD:
                    action = 'fold';
                    break;
                case holdem.Action.RAISE:
                    action = 'raise';
                    amount = msg.deskCoin;
                    break;
                case holdem.Action.ALL_IN:
                    action = 'allin';
                    amount = msg.deskCoin;
                    break;
                default:
                    logging.warn("unsupported player's action", msg);
                    return;
            }
            let playerAction = {
                action: action,
            };
            if (amount) {
                playerAction.amount = msg.deskCoin;
            }
            state.actions.entries.push(playerAction);
            pot += msg.deskCoin || 0;

            // add allined player's action if any
            // this covers most of the cases
            if (allinedPlayers.size > 0) {
                let nextSeat = (msg.seatNum) % state.players.length;
                if (allinedPlayers.has(nextSeat)) {
                    logging.info('adding allin action for seat', {nextSeat});
                    state.actions.entries.push({ action: 'allin' });
                    allinedPlayers.delete(nextSeat);
                }
            }
        }
    };

    const onDealerPosMsg = (msg) => {
        if (roomId === msg.roomId) {
            currentLevel = riseIndex;
            state.big_blind =
                tournamentDetail?.TournamentDetail?.HoldemBlindsConfig[currentLevel - 1]?.BigBlind;
            state.ante = tournamentDetail?.TournamentDetail?.HoldemBlindsConfig[currentLevel - 1]?.Ante;
            state.players = msg.seats.map((seat) => ({
                seat_no: seat.seatNum - 1,
                stack: seat.leftCoin + (seat.deskCoin || 0) + state.ante,
            }));
            state.bb_seat = msg.bbPos - 1;
            state.sb_seat = msg.sbPos - 1;
            state.dealer_seat = msg.dealerPos - 1;
            checkAndFixSeats();
            state.actions.entries = [];
            pot = msg.seats.reduce((acc, seat) => acc + (seat.deskCoin || 0), msg.pot);
            holeCards = [];
            boardCards = [];
            allinedPlayers = new Set([]);
            // get fresh tournament data before each round
            gamePlayer.websocket.send(mttPro.MessageId.MttRealTimeRecordReq, {
                mttId: mttId,
                roomId: roomId,
                fullData: true,
            });
        }
    };

    const checkAndFixSeats = () => {
        // check dealer, sb and bb seats are present in the players list
        let seats = [state.dealer_seat, state.sb_seat, state.bb_seat];
        let playersSeats = state.players.map((player) => player.seat_no);
        if (seats.every((seat) => playersSeats.includes(seat))) {
            return;
        }

        // if some is missing, find bb seat and set sb and dealer accordingly
        let bbSeatIdx = -1;
        for (let i = 0; i < state.players.length; i++) {
            if (state.players[i].seat_no === state.bb_seat) {
                bbSeatIdx = i;
                break;
            }
        }

        if (bbSeatIdx === -1) {
            logging.warn('bb seat not found', null);
            return;
        }

        state.sb_seat = state.players[(bbSeatIdx - 1 + state.players.length) % state.players.length].seat_no;
        if (state.players.length === 2) {
            state.dealer_seat = state.sb_seat;
        } else {
            state.dealer_seat =
                state.players[(bbSeatIdx - 2 + state.players.length) % state.players.length].seat_no;
        }
    };

    const onHoleCardsMsg = (msg) => {
        if (roomId === msg.roomId) {
            for (let p of state.players) {
                if (p.seat_no === seatNum - 1) {
                    let cards = Array.from(msg.cards).map(decodeCard);
                    holeCards = cards;
                    p.hole_cards = cards.join('');
                }
            }
        }
    };

    const onBoardCardsMsg = (msg) => {
        if (roomId === msg.roomId) {
            let cards = Array.from(msg.cards).map(decodeCard);
            boardCards.push(...cards);
            state.actions.entries.push({ action: cards.join('') });
        }
    };

    const onPlayerLeaveMsg = (msg) => {
        if (roomId === msg.roomId && msg.userId === userId) {
            logging.info('PlayerLeaveMsg', undefined);
        }
    };

    const onPlayerStateMsg = (msg) => {
        if (roomId === msg.roomId && msg.state === holdem.Action.ALL_IN) {
            allinedPlayers.add(msg.seatNum - 1);
        }
    }

    const onRiseBlindNotifyMsg = (msg) => {
        if (roomId === msg.roomId) {
            riseIndex = msg.riseIndex;
        }
    };

    const onLeaveRoomRes = (msg) => {
        if (roomId === msg.roomId) {
            logging.info('LeaveRoomRes', msg);
        }
    };

    const onRoundResultMsg = (msg) => {
        if (roomId === msg.roomId) {
            // do nothing
        }
    };

    const onRewardMsg = (msg) => {
        if (mttId === msg.mttId && userId === msg.userId) {
            logging.info('RewardMsg', msg);
            updateProgress({ stats: { chips: 0, rank: msg.rank }, status: 'playing' });
            finish();
        }
    };

    const onMttRealTimeRecordRes = (msg) => {
        if (mttId === msg.mttId) {
            tournamentPlayers = msg.players.map((player) => ({
                stack: player.leftcoin,
                rank: player.rank,
            }));
            tournamentPlayersSize = msg.AllPlayerCount;

            if (msg.AllPlayerCount !== tournamentDetail.TournamentDetail.PlayersCount) {
                    updateTournamentDetails(true);
            }

            const player = msg.players.find((player) => player.userId === userId);
            if (player) {
                stats = {
                    chips: player.leftcoin,
                    rank: player.rank,
                };
            } else {
                // if player is not in the list, compute stats from the last known state
                stats = {
                    chips: state.players.find((player) => player.seat_no === seatNum - 1)?.stack,
                    rank: msg.curPlayer.rank,
                };
            }
        }
    };

    const onGameConnected = (_) => {
        logging.info('onGameConnected');
        // feature: ⁠login / authentication (game)
        gamePlayer.requestUserLogin();
        // feature: heartbeat (game), not neccessary
        gamePlayer.websocket.keepPing();
    };

    const onGameDisconnected = (msg) => {
        logging.info('GameDisconnected', msg);

        // retry with exponential backoff
        if (connectAttempt < maxConnectAttempts) {
            setTimeout(() => {
                gamePlayer.connect()
                connectAttempt++;
            }, Math.pow(2, connectAttempt) * 1000);
        } else {
            onError('GameDisconnected');
        }
    };

    // feature: cancel auto play
    const CancelAutoPlay = () => {
        if (gamePlayer.websocket) {
            gamePlayer.websocket.send(gamePlayer.ids.CancelAutoPlayReq, { roomId: roomId });
        }
    };
    // feature: leave the table (mtt do not have leave table function, this function is just tell server you no longer play mtt, mainly use for visitor)
    const LeaveGame = () => {
        if (roomId) {
            gamePlayer.websocket.send(gamePlayer.ids.LeaveRoomReq, { roomId: roomId });
        }
    };

    const decodeCard = (card) => {
        let numebr = { 10: 'T', 11: 'J', 12: 'Q', 13: 'K', 14: 'A' }[card % 16] || card % 16;
        let suit = { 1: 'd', 2: 's', 4: 'h', 8: 'c' }[card >> 4];
        return numebr + suit;
    };

    const toMillis = (timestamp) => {
        if (timestamp instanceof Date) {
            return timestamp.getTime();
        } else if (
            timestamp &&
            typeof timestamp.seconds === 'number' &&
            typeof timestamp.nanos === 'number'
        ) {
            return timestamp.seconds * 1000 + timestamp.nanos / 1000000;
        } else {
            return 0;
        }
    };

    worldPlayer.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, onConnected);
    worldPlayer.websocket.addMessageHandler(
        commonProto.SocketMessageId.User_Login_Response,
        onUserLoginResponse,
    );
    worldPlayer.websocket.addMessageHandler(
        commonProto.SocketMessageId.Broadcast_Message_Envelope,
        onBroadcastMessageEnvelope,
    );

    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.UserTokenRes, onUserTokenRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.EnterRoomRes, onEnterRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoomSnapshotMsg, onRoomSnapshotMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.MttRoomSnapshotRes, onMttRoomSnapshotRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.SeatOccupiedMsg, onSeatOccupiedMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.AutoPlayMsg, onAutoPlayMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.NeedActionMsg, onNeedActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerActionMsg, onPlayerActionMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.DealerPosMsg, onDealerPosMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.HoleCardsMsg, onHoleCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.BoardCardsMsg, onBoardCardsMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerLeaveMsg, onPlayerLeaveMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.PlayerStateMsg, onPlayerStateMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RiseBlindNotifyMsg, onRiseBlindNotifyMsg);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.LeaveRoomRes, onLeaveRoomRes);
    gamePlayer.websocket.addMessageHandler(gamePlayer.ids.RoundResultMsg, onRoundResultMsg);
    gamePlayer.websocket.addMessageHandler(mttPro.MessageId.RewardMsg, onRewardMsg);
    gamePlayer.websocket.addMessageHandler(mttPro.MessageId.MttRealTimeRecordRes, onMttRealTimeRecordRes);
    gamePlayer.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CONNECTED, onGameConnected);
    gamePlayer.websocket.addMessageHandler(WEBSOCKET_EVENT_ID.ON_CLOSE, onGameDisconnected);

    worldPlayer.connect();
}

module.exports = { signupAndPlay };
