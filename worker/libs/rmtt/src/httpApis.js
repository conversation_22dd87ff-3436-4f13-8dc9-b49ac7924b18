const commonProto = require('./mtt/pb/commonProto.js').commonProto;
const axios = require('axios');
const Config = require('./config.js');
const { logging } = require('shared');

class HttpApis {
    requestMttTournamentList(token, platform, callback, onerror) {
        const { Mtt_Tournament_List_Request, Mtt_Tournament_List_Response } = commonProto;
        try {
            let req = new Mtt_Tournament_List_Request();
            req.PlatForm = platform;
            let reqData = Mtt_Tournament_List_Request.encode(req).finish();
            logging.info('requestMttTournamentList req');
            axios
                .get(`${Config.mttApi}/api/mtt/tournamentList?token=${token}`, {
                    method: 'post',
                    responseType: 'arraybuffer',
                    data: reqData,
                })
                .then((response) => {
                    let arrayBuffer = response.data;
                    // console.log('requestMttTournamentList res', arrayBuffer);
                    // console.log('xhr status ', xhr.status);
                    if (arrayBuffer) {
                        let byteArray = new Uint8Array(arrayBuffer);
                        callback(Mtt_Tournament_List_Response.decode(byteArray));
                    }
                })
                .catch((e) => {
                    logging.error('requestMttTournamentList error', e);
                    onerror(e);
                });
        } catch (e) {
            logging.error('requestMttTournamentList error', e);
            onerror(e);
        }
    }

    requestMttTournamentDetail(token, mttId, callback, onerror) {
        const { MttTournamentDetailRequest, MttTournamentDetailResponse } = commonProto;
        try {
            let req = new MttTournamentDetailRequest();
            req.TournamentId = mttId;
            let reqData = MttTournamentDetailRequest.encode(req).finish();
            logging.info('requestMttTournamentDetail req');
            axios
                .get(`${Config.mttApi}/api/mtt/tournamentDetail?token=${token}`, {
                    method: 'post',
                    responseType: 'arraybuffer',
                    data: reqData,
                })
                .then((response) => {
                    let arrayBuffer = response.data;
                    // console.log('requestMttTournamentDetail res', arrayBuffer);
                    // console.log('xhr status ', xhr.status);
                    if (arrayBuffer) {
                        // logging.info(`requestMttTournamentDetail res: ${arrayBuffer}`);
                        let byteArray = new Uint8Array(arrayBuffer);
                        callback(MttTournamentDetailResponse.decode(byteArray));
                    }
                })
                .catch((e) => {
                    logging.error('requestMttTournamentDetail error', e);
                    onerror(e);
                });
        } catch (e) {
            logging.error('requestMttTournamentDetail error', e);
            onerror(e);
        }
    }

    requestMttPlayerSignUp(inputData, callback, onerror) {
        const { MttPlayerSignupRequest, MttPlayerSignupResponse } = commonProto;
        try {
            let req = new MttPlayerSignupRequest();
            req.TournamentId = inputData.TournamentId;
            req.UserId = inputData.UserId;
            req.TicketId = inputData.TicketId;
            req.UserToken = inputData.UserToken;
            req.PlatForm = inputData.PlatForm;
            req.RegGoldType = inputData.RegGoldType;
            let reqData = MttPlayerSignupRequest.encode(req).finish();
            // logging.info('requestMttPlayerSignUp req', reqData);
            axios
                .get(`${Config.mttApi}/api/mtt/signup`, {
                    method: 'post',
                    responseType: 'arraybuffer',
                    data: reqData,
                })
                .then((response) => {
                    let arrayBuffer = response.data;
                    // logging.info('requestMttPlayerSignUp res', arrayBuffer);
                    // console.log('xhr status ', xhr.status);
                    if (arrayBuffer) {
                        let byteArray = new Uint8Array(arrayBuffer);
                        callback(MttPlayerSignupResponse.decode(byteArray));
                    }
                })
                .catch((e) => {
                    logging.error('requestMttPlayerSignUp error', e);
                    onerror(e);
                });
        } catch (e) {
            logging.error('requestMttPlayerSignUp error', e);
            onerror(e);
        }
    }

    requestMttTournamentStatus(token, mttId, callback, onerror) {
        const { MttTournamentStatusRequest, MttTournamentStatusResponse } = commonProto;
        let req = new MttTournamentStatusRequest();
        req.TournamentId = mttId;
        let reqData = MttTournamentStatusRequest.encode(req).finish();
        // console.log('requestMttPlayerSignUp req', resData);
        axios
            .get(`${Config.mttApi}/api/mtt/tournamentStatus?token=${token}`, {
                method: 'post',
                responseType: 'arraybuffer',
                data: reqData,
            })
            .then((response) => {
                let arrayBuffer = response.data;
                // console.log('requestMttPlayerSignUp res', arrayBuffer);
                // console.log('xhr status ', xhr.status);
                if (arrayBuffer) {
                    let byteArray = new Uint8Array(arrayBuffer);
                    callback(MttTournamentStatusResponse.decode(byteArray));
                }
            })
            .catch((e) => {
                logging.error('requestMttTournamentStatus error', e);
                onerror(e);
            });
    }

    requestMttMultiTable(token, userId, callback, onerror = () => {}) {
        const { MttMultiTableRequest, MttMultiTableResponse } = commonProto;
        let req = new MttMultiTableRequest();
        req.UserId = userId;
        let reqData = MttMultiTableRequest.encode(req).finish();
        // logging.info('requestMttMultiTable req');
        axios
            .get(`${Config.mttApi}/api/mtt/multiTablePlayers?token=${token}`, {
                method: 'post',
                responseType: 'arraybuffer',
                data: reqData,
            })
            .then((response) => {
                let arrayBuffer = response.data;
                // console.log('requestMttMultiTable res', arrayBuffer);
                // console.log('xhr status ', xhr.status);
                if (arrayBuffer) {
                    let byteArray = new Uint8Array(arrayBuffer);
                    callback(MttMultiTableResponse.decode(byteArray));
                }
            })
            .catch((e) => {
                logging.error('requestMttMultiTable error', e);
                onerror(e);
            });
    }

    requestJoinedTournaments(token, callback, onerror) {
        const { Player_Joined_Tournaments_Request, Player_Joined_Tournaments_Response } = commonProto;
        try {
            let req = new Player_Joined_Tournaments_Request();
            let reqData = Player_Joined_Tournaments_Request.encode(req).finish();
            logging.info('requestJoinedTournaments req');
            axios
                .get(`${Config.mttApi}/api/mtt/playerJoinedTournaments?token=${token}`, {
                    method: 'post',
                    responseType: 'arraybuffer',
                    data: reqData,
                })
                .then((response) => {
                    let arrayBuffer = response.data;
                    if (arrayBuffer) {
                        let byteArray = new Uint8Array(arrayBuffer);
                        callback(Player_Joined_Tournaments_Response.decode(byteArray));
                    }
                })
                .catch((e) => {
                    logging.error('requestJoinedTournaments error', e);
                    onerror(e);
                });
        } catch (e) {
            logging.error('requestJoinedTournaments error', e);
            onerror(e);
        }
    }

    requestMttTournamentPlayers(token, tournamentId, nickname, callback, onerror) {
        const { MttTournamentPlayersRequest, MttTournamentPlayersResponse } = commonProto;
        try {
            let req = new MttTournamentPlayersRequest();
            req.TournamentId = tournamentId;
            req.SearchKeyword = nickname;
            let reqData = MttTournamentPlayersRequest.encode(req).finish();
            logging.info('requestMttTournamentPlayers req');
            axios
                .get(`${Config.mttApi}/api/mtt/tournamentPlayers?token=${token}`, {
                    method: 'post',
                    responseType: 'arraybuffer',
                    data: reqData,
                })
                .then((response) => {
                    let arrayBuffer = response.data;
                    if (arrayBuffer) {
                        let byteArray = new Uint8Array(arrayBuffer);
                        callback(MttTournamentPlayersResponse.decode(byteArray));
                    }
                })
                .catch((e) => {
                    logging.error('requestMttTournamentPlayers error', e);
                    onerror(e);
                });
        } catch (e) {
            logging.error('requestMttTournamentPlayers error', e);
            onerror(e);
        }
    }
}

module.exports = { HttpApis };
