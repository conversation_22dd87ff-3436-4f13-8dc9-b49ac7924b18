let http = require('http');
let express = require('express');
var bodyParser = require('body-parser');
const { Player } = require('./player');

let app = express();

let players = [];
let stopConsoleLog = true;

http.createServer(app).listen(31382);

app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json({ limit: '50mb' }));

function createResponse(name, error = 0, message = '', data = '') {
    let temp = { error_code: error, message: message, data: data };
    // console.log('createResponse', name, temp);
    return JSON.stringify(temp);
}

getAllPlayerData = () => {
    let datas = [];
    for (let obj of players) {
        datas.push({
            phone: obj?.phone,
            userId: obj?.dataManager?.userData?.Id,
            nickname: obj?.blDataManager?.userData?.Nickname,
            gold: obj?.blDataManager?.userData?.Gold,
            status: {
                blConnection: obj?.blWorldPlayer?.connectionStatus ? obj?.blWorldPlayer?.connectionStatus : 0,
                connection: obj?.worldPlayer?.connectionStatus ? obj?.worldPlayer?.connectionStatus : 0,
                gameConnection: obj?.gamePlayer?.connectionStatus ? obj?.gamePlayer?.connectionStatus : 0,
                tournament: obj?.worldPlayer?.tournamentStatus ? obj?.worldPlayer?.tournamentStatus : 0,
                tournamentJoin: obj?.worldPlayer?.tournamentJoinStatus
                    ? obj?.worldPlayer?.tournamentJoinStatus
                    : 0,
            },
            error: {
                bl: obj?.blWorldPlayer?.error,
                mtt: obj?.worldPlayer?.error,
                game: obj?.gamePlayer?.error,
            },
        });
    }
    return datas;
};

app.all('/getAllPlayerData', function (req, res) {
    let name = 'getAllPlayerData';
    res.header('Access-Control-Allow-Origin', '*');
    res.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild',
    );
    res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
    if (req.method == 'OPTIONS') {
        res.end();
        return;
    }
    try {
        res.send(createResponse(name, 0, 0, getAllPlayerData()));
        res.end();
    } catch (e) {
        console.log(e);
        res.send(createResponse(name, 1, e));
        res.end();
    }
});

app.all('/startGame', function (req, res) {
    let name = 'startGame';
    res.header('Access-Control-Allow-Origin', '*');
    res.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild',
    );
    res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
    if (req.method == 'OPTIONS') {
        res.end();
        return;
    }
    try {
        let data = req?.query;
        if (data && players.length == 0) {
            let areaCode = data?.areaCode;
            let phoneStart = parseInt(data?.phoneStart);
            let password = data?.password;
            let mttId = parseInt(data?.mttId);
            let playerCount = data?.playerCount;

            for (let i = 0; i < playerCount; i++) {
                players.push(new Player(areaCode, phoneStart + i, password, mttId));
            }
            res.send(createResponse(name));
        } else {
            res.send(createResponse(name, 1, 'Already started MTT ID ' + players[0].mttId));
        }

        res.end();
    } catch (e) {
        console.log(e);
        res.send(createResponse(name, 1, e));
        res.end();
    }
});

app.all('/stopGame', function (req, res) {
    let name = 'stopGame';
    res.header('Access-Control-Allow-Origin', '*');
    res.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Content-Length, Authorization, Accept, X-Requested-With , yourHeaderFeild',
    );
    res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
    if (req.method == 'OPTIONS') {
        res.end();
        return;
    }
    try {
        if (players.length > 0) {
            for (let i = 0; i < players.length; i++) {
                players[i].close();
            }
            players = [];
            res.send(createResponse(name));
        } else {
            res.send(createResponse(name, 2, 'Already stopped'));
        }
        res.end();
    } catch (e) {
        console.log(e);
        res.send(createResponse(name, 1, e));
        res.end();
    }
});

console.log('started');

if (stopConsoleLog) {
    console.log = () => {};
}
