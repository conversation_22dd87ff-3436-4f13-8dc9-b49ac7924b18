import * as fs from 'fs';
import * as https from 'https';
import * as http from 'http';
import * as url from 'url';
import * as querystring from 'querystring';
import U from 'underscore';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { logging } from 'shared';

function request(param: any) {
    const { proxyUrl } = param;
    let cHttp = param.url.startsWith('https') ? https : http;
    param.callback = param.callback || ((r: any, e: any) => console.log(r, e));

    let httpReq: any;
    let nUrl = param.url;

    const timer = setTimeout(() => {
        logging.warn('Request timeout', { url: nUrl });
        httpReq.destroy(new Error('Request timeout'));
        param.callback(null, new Error('Request timeout'));
    }, 30 * 1000);

    let response = function (res: any) {
        if (res.statusCode < 200 || res.statusCode > 299) {
            logging.warn(`Unexpected response code: ${res.statusCode} ${res.statusMessage}`, { url: nUrl });
        }

        let resp = '';
        res.on('data', (data: any) => (resp += data));
        res.on('end', () => {
            clearTimeout(timer);
            try {
                resp = JSON.parse(resp);
            } catch (e) {}
            param.callback && param.callback(resp);
        });
    };

    let content = querystring.stringify(param.data);
    if (param.type == 'get') {
        nUrl += (param.url.includes('?') ? '&' : '?') + content;
        if (proxyUrl) {
            const agent = new HttpsProxyAgent(proxyUrl);
            logging.info(`Get HTTP request with proxy: ${proxyUrl}, url: ${nUrl}`);
            httpReq = cHttp.get(nUrl, { agent }, response);
        } else {
            httpReq = cHttp.get(nUrl, response);
            logging.info(`Get HTTP request without proxy, url: ${nUrl}`);
        }
    } else {
        let postOption: any = url.parse(nUrl);
        postOption.method = 'POST';
        postOption.headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': content.length,
            'User-Agent':
                'Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1',
        };
        if (proxyUrl) {
            postOption.agent = new HttpsProxyAgent(proxyUrl);
        }
        httpReq = cHttp.request(postOption, response);
        logging.info(`Performing POST HTTP request url: ${nUrl}`);
        if (proxyUrl) {
            logging.info(`I have proxy (POST HTTP): ${proxyUrl}`);
        }
        httpReq.write(content);
        httpReq.end();
    }

    httpReq.on('error', (e: any) => {
        clearTimeout(timer);
        logging.warn(`Error with http request: ${e.message}`);
        param.callback(null, e);
    });
}

// ************* 缓存工具 *************
let cacheFile = './cache.json',
    cacheMap: any = null;

let syncCache = U.throttle(() => writeFile(cacheFile, JSON.stringify(cacheMap)), 3000, {
    leading: false,
}); // 每5秒同步一次

let cacheData = {
    getItem: (key: string) => {
        if (!cacheMap) {
            try {
                cacheMap = JSON.parse(readFile(cacheFile) || '{}');
            } catch (e) {
                cacheMap = {};
            }
        }
        return cacheMap[key];
    },
    setItem: (key: string, value: any) => {
        cacheData.getItem(key);
        cacheMap[key] = value;
        syncCache();
    },
};

export function cache(key: string, value?: any, timeoutSec?: number) {
    let isGet = !timeoutSec,
        isKey = 'cache-mp',
        now = new Date().getTime();
    let cMap = cacheData.getItem(isKey) || {};
    let syncCache = () => cacheData.setItem(isKey, cMap);
    if (isGet) {
        let val = cMap[key];
        if (val && val.t >= now) return val.v;
    } else {
        cMap[key] = { t: now + timeoutSec * 1000, v: value };
        syncCache();
    }
    // 间隔一小时清理超时的缓存
    if (!isCD('clear-cache', 3600)) {
        for (const k in cMap) {
            if (cMap[k].t < now) delete cMap[k];
        }
        syncCache();
    }
}

function isCD(key: string, cdSec: number) {
    let cdMap = cacheData.getItem('isCDMap') || {};
    let overtime = cdMap[key],
        curTime = new Date().getTime();
    let isCD = overtime && curTime <= overtime;
    // 清除过期的key
    for (let k in cdMap) {
        if (curTime > cdMap[k]) delete cdMap[k];
    }
    if (!cdMap[key]) cdMap[key] = curTime + cdSec * 1000;
    //cache.setItem("isCDMap", cdMap);
    return !!isCD;
}

function writeFile(filePath: string, txt: string) {
    return fs.writeFileSync(filePath, txt);
}

function readFile(filePath: string) {
    return fs.readFileSync(filePath, 'utf-8');
}

export function randomStr(num: number, str?: string) {
    str = str || '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < num; i++) {
        result += str.charAt(Math.floor(Math.random() * str.length));
    }
    return result;
}

export default { randomStr, cache, U };
