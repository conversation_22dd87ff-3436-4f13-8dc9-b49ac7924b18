import { CurrencyType as pbCurrencyType } from './tools/Enum';
import { pb } from '../proto/ws_protocol';
import { Tools } from './tools/Tools';
import { CurrencyType, GameType, TableData } from 'shared';

export const toTableData = (data: pb.IClubGameSnapshotV3[]): TableData[] => {
    return data.map((d: pb.IClubGameSnapshotV3): TableData => {
        let blinds = [d.small_blind, d.big_blind];
        if (d.straddle) {
            blinds.push(blinds.at(-1) * 2);
        }
        return {
            tableId: d.room_id,
            gameType: 'NLHE',
            gameId: d.game_id,
            gameMode: d.game_mode,
            roomMode: d.room_mode,
            currency: currency(d.currencyType),
            blinds: blinds,
            straddle: d.straddle,
            ante: d.ante,
            playersCount: d.player_count,
            maxPlayers: d.player_count_max,
            leftSeats: d.left_seatnum,
            tableName: Tools.getRoomName(d.iden_num, d.game_id, d.game_mode, d.room_mode),
        };
    });
};

const currency = (currencyType: pbCurrencyType | null): CurrencyType | 'UNKNOWN' => {
    switch (currencyType) {
        case pbCurrencyType.USD:
            return CurrencyType.USD;
        case pbCurrencyType.DIAMOND:
            return CurrencyType.DIAMOND;
        case pbCurrencyType.GOLD:
            return CurrencyType.GOLD;
        default:
            return 'UNKNOWN';
    }
};
