import { CardItem } from './data/RoomData';
import { Card<PERSON><PERSON>, CardSuit } from './tools/Enum';
import { protocol } from '../proto/gs_protocol';
import { GameMode, GameStateInterface, GameType, logging, StrategyResponseAction } from 'shared';

export type CheckIfRebuyNeededFunc = (stake: number, buyIn: number, rebuyThreshold: number) => boolean;

export class GameState implements GameStateInterface {
    gameuuid: string = '';
    game_type: GameType = GameType.NLHE;
    game_mode_code: GameMode = GameMode.NORMAL;
    roomid: string = '';
    big_blind: number = 0;
    ante: number = 0;
    actions: GameActions;
    players: GamePlayer[];
    dealer_seat: number = -1;
    sb_seat: number = -1;
    bb_seat: number = -1;
    straddle_seat: number = -1;
    post_seats?: number[];

    constructor(
        gameuuid: string,
        roomid: string,
        big_blind: number,
        ante: number,
        gameModeCode: GameMode = GameMode.NORMAL,
        gameTypeCode: GameType = GameType.NLHE,
    ) {
        this.gameuuid = gameuuid;
        this.roomid = roomid;
        this.big_blind = big_blind;
        this.ante = ante;
        this.actions = new GameActions();
        this.players = [];
        this.game_mode_code = gameModeCode;
        this.game_type = gameTypeCode;
    }

    resetState({ ante, gameModeCode }: { gameModeCode: GameMode; ante: number }) {
        logging.info(`[state] resetState ante changed: ${this.ante} -> ${ante}`);
        this.game_mode_code = gameModeCode;
        this.ante = ante;
        this.actions = new GameActions();
        this.players = [];
        this.dealer_seat = -1;
        this.sb_seat = -1;
        this.bb_seat = -1;
        this.straddle_seat = -1;
        this.post_seats = undefined;
    }

    updateAnteGameMode(gameModeCode: GameMode, ante: number) {
        this.game_mode_code = gameModeCode;
        this.ante = ante;
    }

    addCardsAction(parsedCards: string) {
        let action = new GameAction();
        action.action = parsedCards;
        this.actions.entries.push(action);
    }

    addPlayer(playerUid: string, seatId: number, stake: number) {
        this.players.push(new GamePlayer(playerUid, seatId, stake));
    }

    addAction(pkPlayerAction: protocol.NoticePlayerAction) {
        let action = new GameAction();
        switch (pkPlayerAction.action_type) {
            case protocol.ActionType.Enum_Action_Check:
                action.action = StrategyResponseAction.CHECK;
                break;
            case protocol.ActionType.Enum_Action_Fold:
                action.action = StrategyResponseAction.FOLD;
                break;
            case protocol.ActionType.Enum_Action_Call:
                action.action = StrategyResponseAction.CALL;
                break;
            case protocol.ActionType.Enum_Action_Bet:
                action.action = StrategyResponseAction.BET;
                break;
            case protocol.ActionType.Enum_Action_Raise:
                action.action = StrategyResponseAction.RAISE;
                break;
            case protocol.ActionType.Enum_Action_Allin:
                action.action = StrategyResponseAction.ALLIN;
                break;
            default:
                console.error('unknown action type: ' + pkPlayerAction.action_type);
                return;
        }
        if (pkPlayerAction.amount > 0) {
            action.amount = pkPlayerAction.amount;
        }
        action.seat_no = pkPlayerAction.last_action_seat_id;
        this.actions.entries.push(action);
    }

    getCurrentPlayer(playerUid: string): GamePlayer {
        return this.players.find((p) => p.uid === playerUid);
    }

    getCardsArray(player: GamePlayer): string[] {
        if (player?.hole_cards) {
            return [player.hole_cards.slice(0, 2), player.hole_cards.slice(2)];
        }
        return [];
    }

    getCommunityCards(): string[] {
        // the action is a card action if it starts with uppercase letter or a number
        return this.actions.entries
            .filter((a) => /^[A-Z0-9]/.test(a.action))
            .map((a) => a.action)
            .flatMap((c) => (c.length === 6 ? [c.slice(0, 2), c.slice(2, 4), c.slice(4)] : c));
    }
}

class GameActions {
    entries: GameAction[] = [];
}

export class GameAction {
    action: StrategyResponseAction | string = '';
    amount?: number;
    seat_no?: number;
    probability?: number;
}

export class GamePlayer {
    uid: string = '';
    seat_no: number = 0;
    stack: number = 0;
    hole_cards?: string;

    constructor(uid: string, seat_no: number, stack: number) {
        this.uid = uid;
        this.seat_no = seat_no;
        this.stack = stack;
    }

    setHoleCards(cards: string) {
        if (cards.trim() && cards !== this.hole_cards) {
            logging.withTag('PKW').info(`[state] Hole cards changed: ${this.hole_cards} -> ${cards}`);
            this.hole_cards = cards;
            logging.setHoleCards(this.hole_cards);
        }
    }

    getCardsArray(): string[] {
        if (this.hole_cards) {
            return [this.hole_cards.slice(0, 2), this.hole_cards.slice(2)];
        }
        return [];
    }
}

// This function inputs a list of CardItems
// Transform each card to a symbol, and join them together and outputs a resulting string of cards
// It may return empty string if the card is hidden (e.g. number: 256, suit: 256)
export const transformCardItemsToSymbol = (cards: CardItem[]): string => {
    const parsedCards = cards.map(cardToSymbol).join('');
    // if there was a hidden card - we do not suggest the cards to be valid
    // therefore not showing anything
    if (parsedCards.includes('?')) {
        return '';
    }
    return parsedCards;
};

const cardToSymbol = (card: CardItem): string => {
    let result = '';
    switch (card.number) {
        case CardNum.CARD_2:
            result += '2';
            break;
        case CardNum.CARD_3:
            result += '3';
            break;
        case CardNum.CARD_4:
            result += '4';
            break;
        case CardNum.CARD_5:
            result += '5';
            break;
        case CardNum.CARD_6:
            result += '6';
            break;
        case CardNum.CARD_7:
            result += '7';
            break;
        case CardNum.CARD_8:
            result += '8';
            break;
        case CardNum.CARD_9:
            result += '9';
            break;
        case CardNum.CARD_10:
            result += 'T';
            break;
        case CardNum.CARD_J:
            result += 'J';
            break;
        case CardNum.CARD_Q:
            result += 'Q';
            break;
        case CardNum.CARD_K:
            result += 'K';
            break;
        case CardNum.CARD_A:
            result += 'A';
            break;
        case CardNum.CARD_HIDDEN:
            return '?';
        default:
            throw new Error(`unknown card number: ${card.number}, card: ${JSON.stringify(card)}`);
    }
    switch (card.suit) {
        case CardSuit.CARD_DIAMOND:
            result += 'd';
            break;
        case CardSuit.CARD_CLUB:
            result += 'c';
            break;
        case CardSuit.CARD_HEART:
            result += 'h';
            break;
        case CardSuit.CARD_SPADE:
            result += 's';
            break;
        case CardSuit.SUIT_HIDDEN:
            return '?';
        default:
            throw new Error(`unknown card suit: ${card.suit}, card: ${JSON.stringify(card)}`);
    }
    return result;
};
