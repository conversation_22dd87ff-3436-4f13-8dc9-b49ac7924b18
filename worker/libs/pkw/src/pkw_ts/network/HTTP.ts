import { inflate } from 'pako';

/**
 * http 请求类型
 */
enum HttpRequestType {
    GET = 'get',
    POST = 'post',
    PUT = 'put',
    DELETE = 'delete',
    UNKNOWN = 'unknown',
}

/**
 * http 解析类型
 */
enum HttpParseType {
    NONE = 0, // 无 (默认完整参数)
    SEND_ZIP, // 发送端 zip (去掉签名等参数)
    RECEIVE_ZIP, // 接收端 zip (去掉签名等参数)
    BOTH_ZIP, // 发送接收都 zip (去掉签名等参数)
}

// HTTP请求信息
export class RequestInfo {
    //请求数据
    jsonData: Object = null;
    //接口名称
    server_interface_nme: string = '';
    //回调
    handler: Function = null;
    //请求类型(默认 POST)
    requestType: HttpRequestType = null;
    //解析类型(默认 NONE)
    parseType: HttpParseType = null;
    //是否显示loading(默认 true)
    isshowloading: boolean = true;
}
export class HTTP {
    private static _g_instence: HTTP = null;

    public HttpParseType = HttpParseType;
    public HttpRequestType = HttpRequestType;
    public CONN_TIME_OUT: number = 10;
    public READ_TIME_OUT: number = 10;

    private _requestInfoMap: RequestInfo[] = [];

    /**
     * 获取请求数据
     */
    public getRequestInfo(url: string): RequestInfo {
        return this._requestInfoMap[url];
    }

    /**
     * 清除缓存数据
     */
    public clearRequestInfo() {
        this._requestInfoMap.splice(0, this._requestInfoMap.length - 1);
    }

    unzip(response: string): string {
        let strData = window.atob(response);
        // Convert binary string to character-number array
        let charData = strData.split('').map(function (x) {
            return x.charCodeAt(0);
        });
        // Turn number array into byte-array
        let binData = new Uint8Array(charData);
        // unzip
        response = inflate(binData, { to: 'string' });
        // Convert gunzipped byteArray back to ascii string:
        //respone = String.fromCharCode.apply(null, new Uint16Array(data));
        return response;
    }

    public static getInstance(): HTTP {
        if (!HTTP._g_instence) {
            HTTP._g_instence = new HTTP();
        }
        return HTTP._g_instence;
    }
}
