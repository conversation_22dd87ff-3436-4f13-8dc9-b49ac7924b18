export class NativeEvent {
    private static instance: NativeEvent;

    public static getInstance(): NativeEvent {
        if (!this.instance) {
            this.instance = new NativeEvent();
        }
        return this.instance;
    }

    public GetLocation(): object {
        return {
            latitude: 10,
            longitude: 10,
        };
    }

    public CheckNetWork(): boolean {
        return true;
        //  return this.invokeSyncFunc(NATIVE_KEY_MAP.KEY_IS_NETWORK_AVAILABLE) == window.NativeStringReturnTrue;
    }
}
