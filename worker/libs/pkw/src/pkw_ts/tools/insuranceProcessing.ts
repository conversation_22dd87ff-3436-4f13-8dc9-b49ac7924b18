import { logging, weightedRandomSelection } from 'shared';
import { BettingRoundType } from './Enum';

export const LOW_OUTS_COUNT = 3;
export const MEDIUM_OUTS_COUNT = 7;
export const HIGH_OUTS_COUNT = 12;
export const SERVER_COVERAGE_AMOUNT = {
    LOW: 12,
    MEDIUM_LOW: 20,
    MEDIUM_HIGH: 33,
    BREAK_EAVEN: 50,
    FULL_POT: 100,
};

const percentageToFractionMap = new Map([
    [SERVER_COVERAGE_AMOUNT.LOW, '1/8'],
    [SERVER_COVERAGE_AMOUNT.MEDIUM_LOW, '1/5'],
    [SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH, '1/3'],
    [SERVER_COVERAGE_AMOUNT.BREAK_EAVEN, 'break even'],
    [SERVER_COVERAGE_AMOUNT.FULL_POT, 'full pot'],
]);

export const calculateInsuranceAmount = (
    outsCount: number,
    investmentAmount: number,
    coverageLimit: number,
): number => {
    const shouldBuy = shouldBuyInsurance(outsCount);

    // if (!shouldBuy) {
    //     return 0;
    // }


    const coveregeAmountsWaights = getCoverageAmountWeights(outsCount);
    const coverageAmount = weightedRandomSelection(
        coveregeAmountsWaights,
        (entry) => entry.weight,
    ).covarageAmount;

    let insuranceAmount = Math.round(investmentAmount * (coverageAmount / 100));

    // we need to check on 0, since game server sometimes sends incorrect numbers.
    if (coverageLimit != 0) {
        // we couldn't insure more then 33% of the investmanet at first proposal.
        const coverageLimitAmount = Math.round(investmentAmount * (SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH / 100));
        insuranceAmount = Math.min(insuranceAmount, coverageLimitAmount);
    }

    logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
        outsCount,
        shouldBuy,
        investmentAmount,
        investmentToCover: percentageToFractionMap.get(
            coverageAmount === coverageLimit ? SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH : coverageAmount,
        ),
        insuranceAmount,
    });

    return insuranceAmount;
};

const shouldBuyInsurance = (outsCount: number): boolean => {
    let buyProbability: number;

    if (outsCount <= LOW_OUTS_COUNT) {
        buyProbability = 0.1; // Low probability - opponent has few ways to improve
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        buyProbability = 0.25; // Medium-low probability - moderate opponent threat
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        buyProbability = 0.35; // Medium probability - significant opponent threat
    } else {
        buyProbability = 0.55; // High probability - opponent has many ways to improve
    }

    return buyProbability > Math.random();
};

const getCoverageAmountWeights = (outsCount: number): { covarageAmount: number; weight: number }[] => {
    if (outsCount <= LOW_OUTS_COUNT) {
        // Few opponent outs - lower risk, prefer smaller coverage amounts
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.LOW,
                weight: 50,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 35,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 15,
            },
        ];
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        // Medium opponent outs - moderate risk, balanced coverage
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.LOW,
                weight: 30,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 40,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 10,
            },
        ];
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        // More opponent outs - higher risk, prefer medium to high coverage
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_LOW,
                weight: 25,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 35,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 30,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.FULL_POT,
                weight: 10,
            },
        ];
    } else {
        // Many opponent outs - high risk, prefer higher coverage amounts
        return [
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.MEDIUM_HIGH,
                weight: 20,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.BREAK_EAVEN,
                weight: 40,
            },
            {
                covarageAmount: SERVER_COVERAGE_AMOUNT.FULL_POT,
                weight: 40,
            },
        ];
    }
};
