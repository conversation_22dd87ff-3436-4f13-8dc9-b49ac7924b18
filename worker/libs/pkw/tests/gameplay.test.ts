import { mock, test } from 'node:test';
import assert from 'node:assert';

import { GameMode, GameType, JobType, PkwUserData, RoomStartParams } from "shared";
import { PkwMain } from "../src/pkw_ts/PkwMain";
import pkwGame from '../src/pkw_ts/pkwGame';
import cv from '../src/pkw_ts/cv';
import pkwRoom from '../src/pkw_ts/pkwRoom';
import { domainManager } from '../src/pkw_ts/network/DomainManager';
import { NetWork } from '../src/pkw_ts/network/NetWork';
import { NetWorkProxy } from '../src/pkw_ts/network/NetWorkProxy';
import { GameId } from '../src/pkw_ts/tools/Enum';
import { pb as world_pb } from '../src/proto/ws_protocol';
import { protocol as game_pb } from '../src/proto/gs_protocol';
import { UserActionEnum } from '../src/pkw_ts/pkwUser';
import { logging } from 'shared';

Object.assign(logging, {
    ...console,
    withTag: (tag: string) => console,
});

const GAME_ID = GameId.Texas;
const GAME_MODE = GameMode.NORMAL;
const GAME_TYPE_CODE = GameType.NLHE;
const testLoginData: PkwUserData = {
    pkwAuthData: {
        uid: 123456,
        client_type: 666,
        gate_addr: ['testgate.com'],
        token: '<token1>',
    },
    user: {
        nickname: 'testuser',
    },
} as PkwUserData;
const roomId = 42;
const roomParams = {
    buyInMultiplier: 169,
} as RoomStartParams;

async function delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

const gameSnapshot: game_pb.NoticeGameSnapshot = {
    roomid: roomId,
    gameid: GAME_ID,
    tstate: {
        players: [],
    },
    params: {}
} as any;

const lobbyTestRoom = { // also _roomSt
    iden_num: 666,
    game_mode: 0,
    game_id: GameId.Texas as number,
    room_id: roomId,
    player_count_max: 8,
    ante: 1,
    big_blind: 1,
    buyin_max: 1000,
    buyin_min: 100,
} as world_pb.IClubGameSnapshotV3;

function applyMocks() {
    NetWorkProxy.prototype.decodePB = (_, pbuf) => {
        return pbuf;
    };
    NetWorkProxy.prototype.encodePB = (_, msgData) => {
        return msgData;
    };
    cv.gamePB.lookupType = (type) => {
        return {
            encode: (msg) => ({ finish: () => msg }),
            decode: (msg) => msg,
        };
    };
    // we don't need these in tests
    clearInterval(cv.netWorkManager['_worldHeartBeatId']);
    clearInterval(cv.netWorkManager['_gameHeartBeatId']);
}


test('PKW gameplay: login, join room, buy in, sit down', async () => {
    mock.method(NetWork, 'getInstance', () => NetworkMock);

    await PkwMain.init('', GAME_ID as number, GAME_MODE, GAME_TYPE_CODE);

    assert(pkwGame["_gameModeCode"] == GAME_MODE);
    assert(pkwGame["_gameTypeCode"] == GAME_TYPE_CODE);
    assert(pkwRoom["_gameId"] == GAME_ID as number);
    assert(pkwRoom["_gameType"] == 1);

    applyMocks();

    const loginCallback = mock.fn();
    pkwRoom.setLoginServerCB(loginCallback);
    PkwMain.pkwPlay(testLoginData, JobType.PLAY, roomId, roomParams, () => {});

    assert.equal(cv.dataHandler.getUserData().user_id, '123456');
    assert.equal(cv.dataHandler.getUserData().nick_name, 'testuser');
    assert(cv.config.GET_CLIENT_TYPE() == 666);
    assert(domainManager.getServerInfo().gate_server == 'testgate.com');

    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_Logon_Response,
        { mttData: { token: '<token>' } }
    );

    assert.equal(pkwRoom['_pkwUser'].currentStatus, 'logged_in');
    assert.deepEqual(loginCallback.mock.calls[0].arguments[0], { mtt: { token: '<token>' } });

    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_Login_Notice,
        { roomid: roomId, gameid: GAME_ID }
    );

    assert.equal(pkwRoom['_pkwUser'].userActions[0], UserActionEnum.joinRoom);
    assert(cv.roomManager['current_roomId'] == roomId);
    assert(cv.roomManager['current_gameId'] == GAME_ID);
    assert.equal(pkwRoom['_pkwUser'].currentStatus, 'logged_in_joined_room');

    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_JoinRoom_Response,
        { roomid: roomId, gameid: GAME_ID, error: 1 }
    );
    assert.equal(cv.GameDataManager.tRoomData.u32RoomId, roomId);
    assert.equal(pkwRoom['_pkwUser'].currentStatus, 'in_room');

    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_Game_Snapshot_Notice,
        gameSnapshot,
    );

    const lobbyDataCallback = mock.fn();
    pkwRoom.setLobbyDataCb(lobbyDataCallback);
    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_ClubCurrentBoardV2_Notice,
        { list: [lobbyTestRoom], total: 1 }
    );

    await delay(10);
    // console.log('lobbyDataCallback', lobbyDataCallback.mock.calls[0].arguments[0]);
    assert.equal(lobbyDataCallback.mock.calls[0].arguments[0][0].tableName, 'HL0666');

    const requestBuyInMock = mock.fn();
    cv.gameNet.RequestBuyin = requestBuyInMock as any;
    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_SitDown_Response,
        { error: 32 }, // needBuyin
    );

    await delay(10);
    assert(requestBuyInMock.mock.calls[0].arguments[1] === 169);

    const requestSitDownMock = mock.fn();
    cv.gameNet.RequestSitdown = requestSitDownMock as any;

    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_Buyin_Response,
        { error: 1 }, // success
    );

    await delay(10);
    assert.equal(requestSitDownMock.mock.callCount(), 1);

    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_SitDown_Response,
        { error: 1 }, // success
    );

    assert.equal(pkwRoom['_pkwUser'].currentStatus, 'sat_down');

    // cleanup
    console.log('=='.repeat(20), 'test cleanup', '=='.repeat(20));

    PkwMain.finishGame();
});

test('PKW gameplay: minimal example', async () => {
    mock.method(NetWork, 'getInstance', () => NetworkMock);

    await PkwMain.init('', GAME_ID as number, GAME_MODE, GAME_TYPE_CODE);
    applyMocks();

    PkwMain.pkwPlay(testLoginData, JobType.PLAY, roomId, roomParams, () => {});

    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_Logon_Response,
        { mttData: { token: '<token>' } }
    );
    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_Login_Notice,
        { roomid: roomId, gameid: GAME_ID }
    );
    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_JoinRoom_Response,
        { roomid: roomId, gameid: GAME_ID, error: 1 }
    );
    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_Game_Snapshot_Notice,
        gameSnapshot,
    );
    FakeWS.receiveWorldMessage(world_pb.MSGID.MsgID_ClubCurrentBoardV2_Notice,
        { list: [lobbyTestRoom], total: 1 }
    );
    FakeWS.receiveGameMessage(game_pb.MSGID.MsgID_SitDown_Response,
        { error: 1 }, // success
    );

    console.log('=='.repeat(20), 'test cleanup', '=='.repeat(20));
    PkwMain.finishGame();
});


const NetworkMock = {
    handlers: new Map<number, Map<number, Function>>(),
    isConnected: () => true,
    isEncrypt: () => false,
    getInstance: () => NetworkMock,
    connect: () => {},
    registerMsg: (serverId, msgid, fn) => {
        NetWork.prototype.registerMsg.call(NetworkMock, serverId, msgid, fn);
    },
    sendMsg: (data) => {
        console.log('Mock sendMsg called', data);
    },
    onMessage: (serverId, messageId, msg) => {
        const handlers = NetworkMock.handlers.get(serverId);
        if (handlers) {
            const handler = handlers?.get(messageId)?.(msg);
            if (handler) {
                handler(msg);
            }
        }
    }
}

const FakeWS = {
    receiveWorldMessage(messageId, msg) {
        NetworkMock.onMessage(GameId.World, messageId, msg);
    },
    receiveGameMessage(messageId, msg) {
        NetworkMock.onMessage(GameId.Texas, messageId, msg);
    }
}

