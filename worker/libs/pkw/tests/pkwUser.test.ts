import { test } from 'node:test';
import assert from 'node:assert';
import { UserActionEnum, PkwUser } from '../src/pkw_ts/pkwUser';

// Test adding actions to empty userActions
test('prependUserActions adds to empty list', () => {
    const user = new PkwUser(1, [], []);
    user.prependUserActions(UserActionEnum.login);
    assert.deepEqual(user.userActions, [UserActionEnum.login]);
});

// Test not duplicating existing actions
test('prependUserActions does not add duplicates', () => {
    const user = new PkwUser(1, [UserActionEnum.login], []);
    user.prependUserActions(UserActionEnum.login);
    assert.deepEqual(user.userActions, [UserActionEnum.login]);
});

// Test prepending multiple new actions in order
test('prependUserActions prepends new actions in order', () => {
    const user = new PkwUser(1, [UserActionEnum.requestLobbyData], []);
    user.prependUserActions(UserActionEnum.login, UserActionEnum.joinRoom);
    assert.deepEqual(
        user.userActions,
        [UserActionEnum.login, UserActionEnum.joinRoom, UserActionEnum.requestLobbyData]
    );
});

// Test mixing existing and new actions without duplicates
test('prependUserActions mixes new and existing without duplicates', () => {
    const user = new PkwUser(1, [UserActionEnum.login, UserActionEnum.requestLobbyData], []);
    user.prependUserActions(UserActionEnum.login, UserActionEnum.joinRoom);
    assert.deepEqual(
        user.userActions,
        [UserActionEnum.login, UserActionEnum.joinRoom, UserActionEnum.requestLobbyData]
    );
});

// Test adding multiple new actions when some already exist
test('prependUserActions with multiple actions and duplicates', () => {
    const existing = [UserActionEnum.login, UserActionEnum.requestLobbyData, UserActionEnum.sitDown];
    const user = new PkwUser(1, existing.slice(), []);
    user.prependUserActions(
        UserActionEnum.login,
        UserActionEnum.requestLobbyData,
        UserActionEnum.joinRoom,
        UserActionEnum.sitDown
    );
    assert.deepEqual(
        user.userActions,
        [
            UserActionEnum.login,
            UserActionEnum.requestLobbyData,
            UserActionEnum.joinRoom,
            UserActionEnum.sitDown
        ]
    );
});

