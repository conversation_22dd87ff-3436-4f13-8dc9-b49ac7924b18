import { EncryptUtils, Utils, RobotHttpReq } from 'pkw'
import { BattleRoomMsg } from './protobuf/MsgDeliverRespProto';

import { User } from './types';
import { createLogger } from './logging';
import { PlatformUserData } from 'shared';

const console = createLogger("FriendsHttpApi");

interface HttpResponseWithErrorInfo {
    errorCode: number;
    errMsg: string;
    sysTime: number;
}

let currentUser: User;
export function setCurrentUser(user: User) {
    currentUser = user;
}

export async function generateGameAESKey(roomId) {
    const rasPubKeyResp: any = await wpkHttpRequest("/system/getGameRSAPubKey", {}, { roomId });
    const rasPubKey = rasPubKeyResp?.data;

    if (!rasPubKey) {
        return null;
    }
    currentUser.gameAesKey = Utils.randomStr(6);
    return EncryptUtils.encryptRSA(currentUser.gameAesKey, rasPubKey);
}

// for some reason, if queryParams are passed, body needs to be {}
export async function wpkHttpRequest(path: string, body?: any, queryParams?: any): Promise<HttpResponseWithErrorInfo> {
    const response = await RobotHttpReq.wpkRequest(currentUser as PlatformUserData, path, body, true, null, queryParams) as HttpResponseWithErrorInfo;
    if (!response || response.errorCode !== 0) {
        console.error(`HTTP request failed`, response.errMsg, { path, body, queryParams, response });
        throw new Error(`Request to ${path} failed with ${response?.errorCode}: ${response?.errMsg}`);
    }
    return response;
}

export interface CreateRoomParams {
    roomTitle?: string; // Room title
    duration: number; // Game duration in minutes (30, 60, 120, 180, 300)
    isNeedAgreeSit: number; // Whether authorization is required to sit down (0 or 1)
    grade: number; // Game level
    maxScoreMultiple: number; // Exchange scoreboard upper limit (-1 means no upper limit)
    minScoreMultiple: number; // Exchange scoreboard lower limit
    isAnte: number; // Whether ante is required (0 or 1)
    ante: number; // Ante score
    gameFund: number; // Game fund upper limit (0 means no fund withdrawal)
    gameFundRate: number; // Game fund upper limit ratio (0 means no fund withdrawal)
    isSendDouble: number; // Whether to support ALLIN and send twice (0 or 1)
    roomType: number; // Room type (0: Club room, 1: Password room)
    isLimitIp: number; // Whether to limit IP and GPS (0 or 1)
    gamePersonNum: number; // Number of players
    playType: number; // Game play type (0: Normal, 1: Short deck, 2: Omaha, 3: SNG)
    isDoubleAnte: number; // Whether to enable 2x ante (0 or 1)
    clubId: number; // Club ID
    isLimitScore: number; // Whether to limit score (0 or 1)
    shortCompareType: number; // Short hand game comparison card type (0: Straight is better, 1: Three of a kind is better)
    useWallet: number; // Whether to use wallet (0 or 1)
    forceLive: number; // Force video (0: Not forced, 1: Forced)
    allowLive: number; // Allow video (0: Not allowed, 1: Allowed)
    isThirdBlind: number; // Whether to enable the third blind (0 or 1)
    isActSeeCard: number; // Whether to enable operation to see the card (0 or 1)
    isRandSeat: number; // Whether to enable random seating (0 or 1)
}

export async function createRoom(roomParams: CreateRoomParams): Promise<HttpResponseWithErrorInfo> {
    return wpkHttpRequest("/battle/createRoom", {}, roomParams);
}

interface RoomRecordByCreateViewResponse extends HttpResponseWithErrorInfo {
    roomList: BattleRoomMsg[];
    roomCount: number;
}

export async function getRoomsCreatedByUser(): Promise<RoomRecordByCreateViewResponse> {
    return await wpkHttpRequest("/hall/getRoomRecordByCreateView") as RoomRecordByCreateViewResponse;
}

interface BalanceDiamond {
    balanceDiamond: number;
    giftBalanceDiamond: number;
    gift2Balance: number;
    freezeDiamond: number;
    bossFreezeDiamond: number;
    unfreezeTime: string;
    freezeCount: number;
}
async function getBalanceDiamond(): Promise<BalanceDiamond> {
    return (await wpkHttpRequest('/deal/getBalanceDiamond') as any).data as BalanceDiamond;
}

async function getUserWalletInfo() {
    return await wpkHttpRequest('/userWallet/getUserWalletInfo');
}

async function systemDevTest(param) {
    return wpkHttpRequest("/system/devTest.anon", param);
}

interface CheckIntoRoomResponse extends HttpResponseWithErrorInfo {
    roomId: number;
    roomNumber: number;
    gamePersonNum: number;
    urlPath: string;
    playType: number;
    spQuickOpen: boolean;
    guessHandBetStatus: number;
    poolingHandNum: number;
}
export async function checkUserIntoRoomByRoomId(roomId: number): Promise<CheckIntoRoomResponse> {
    return await wpkHttpRequest("/battle/check_user_into_room_by_roomid", {}, { roomId }) as CheckIntoRoomResponse;
}

export async function getPlayerClubs() {
    return (await wpkHttpRequest('/getClubList.ajax') as any).data.clubList;
}
