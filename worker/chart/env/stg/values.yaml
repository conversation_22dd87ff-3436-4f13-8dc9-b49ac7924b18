environment: &environment stg

service:
  - type: ClusterIP
    port: 3000
    targetPort: 3000

configmap:
  STRATEGY_SERVICE_URL: http://gtoglue.stg.aceguardian.io
  WPK_URL: "http://**************:83/wepoker"
  WPTGO_URL: "https://api.stg.wptg.a5-labs-cloud.com"
  WPTGO_WS_URL: "wss://wptg-gate-stg.a5labsapp.co"
  MTT_CONFIG_URL: "http://*************:29011/appfile/url_config"
  MTT_CONFIG_TTL_SECONDS: 28800
  MTT_CONFIG_MTTWORLD: "ws://*************:3001"
  MTT_CONFIG_MTTGAME: "ws://*************:4001"
  MTT_CONFIG_MTTAPI: "http://*************:22001"
  MTT_PROTOBUF_VERSION: "v2"
  UNLEASH_API_URL: "https://unleashedge.stg.fungamer.io/api"

deployment:
  image:
    repository: 124355651851.dkr.ecr.ap-southeast-1.amazonaws.com/fg/botworker
    tag: 0.30.29-stg
  terminationGracePeriodSeconds: "600"
  revisionHistoryLimitCount: 5
  nodePoolTolerationValue: default
  resources:
    limits:
      cpu: 2
      memory: "12228Mi"
    requests:
      cpu: "512m"
      memory: "1024Mi"
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/metrics"

secrets:
  - type: ESO
    name: documentdb-endpoint
    secrets:
      MONGO_HOST: data_storage/documentdb.elastic.endpoint
  - type: ESO
    name: redis-endpoint
    secrets:
      REDIS_HOST: data_storage/elasticache.manager.endpoint
  - type: ESO
    name: mongo-user
    secrets:
      MONGO_USER:
        key: data_storage/documentdb.elastic.credentials
        property: username
  - type: ESO
    name: mongo-password
    secrets:
      MONGO_PASSWORD:
        key: data_storage/documentdb.elastic.credentials
        property: password
  - type: ESO
    name: strategy-service-token
    secrets:
      STRATEGY_SERVICE_TOKEN: bots/svc.botworker.strategy_service_token
  - type: ESO
    name: secret-key
    secrets:
      SECRET_KEY: bots/svc.botworker.secret_key
  - type: ESO
    name: unleash-api-token
    secrets:
      UNLEASH_API_TOKEN: management/unleash.edge.api_token

ingress:
  - ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    rules:
      - host: botworker.stg.fungamer.io
        paths:
          - path: /dashboard
            pathType: Prefix
            number: 3000
          - path: /arena
            pathType: Prefix
            number: 3000

hpa:
  - name: botworker
    targetDeployment:
      apiVersion: apps/v1
      kind: Deployment
      name: botworker
    minReplicas: 1
    maxReplicas: 5
    metrics:
      - type: Pods
        name: bullmq_node_utilization
        averageValue: "0.7"
    behavior:
      scaleUp:
        stabilizationWindowSeconds: 60
        selectPolicy: Max
        policies:
          - type: Pods
            value: 1
            periodSeconds: 15
      scaleDown:
        stabilizationWindowSeconds: 600
        selectPolicy: Min
        policies:
          - type: Pods
            value: 1
            periodSeconds: 60
