import { UnrecoverableError } from 'bullmq';
import { AppType, CurrencyType, JobData, JobDataHandler, JobType, logging, PkwUserData, TableData } from 'shared';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';
import { pkwConfigurations } from './pkwConfigurations';
import { LobbyDataFunction } from 'pkw/dist/pkw_ts/pkwRoom';

class PkwScanAdapterClass implements GamePlatformAdapter {

    getTablesProcessor(onMessage: JobDataHandler, supportedAppTypes: AppType[]): LobbyDataFunction {
        return (data: TableData[]) => {
            const tables: TableData[] = [];
            for (const table of data) {
                const appId = supportedAppTypes.find((appId) => {
                    const config = pkwConfigurations[appId];
                    return (
                        table.gameId === config.gameId
                        && table.gameMode === config.gameMode
                        && config.currencies.includes(table.currency as CurrencyType)
                    );
                });

                if (appId) {
                    tables.push({
                        ...table,
                        appId,
                    });
                }
            }
            onMessage({ tables });
        };
    }

    async init(proxyUrl?: string): Promise<void> {
        await PkwMain.initScan(proxyUrl);
    }

    async start(userData: PkwUserData, jobType: JobType, _: number, params: JobData, onMessage: JobDataHandler): Promise<void> {
        if (jobType !== JobType.SCAN) {
            throw new UnrecoverableError('Unsupported game platform action: check');
        }
        const supportedAppTypes = params.appIds || [];
        logging.info('PkwScanAdapter.start', { supportedAppTypes: supportedAppTypes });
        return PkwMain.pkwScan(userData, jobType, this.getTablesProcessor(onMessage, supportedAppTypes));
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Scan Adapter] stop', jobType);
    }

    isMtt(): boolean {
        return false;
    }
}

export const PkwScanAdapter = new PkwScanAdapterClass();
