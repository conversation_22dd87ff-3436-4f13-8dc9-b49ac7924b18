import { GamePlatformAdapter } from '../models/model';
import { MttConfig, MttMain } from 'rmtt';

import { JobType, JobDataHandler, MttUserData } from 'shared';
import redis from '../redis';

class RMtt implements GamePlatformAdapter {
    async init(): Promise<void> {
        if (process.env.R4_CONFIG!) {
            try {
                const envMttConfig = JSON.parse(process.env.R4_CONFIG!);
                console.log('ENV MTT CONFIG', envMttConfig);
                MttConfig.mttWorld = envMttConfig.mttWorld;
                MttConfig.mttGame = envMttConfig.mttGame;
                MttConfig.mttApi = envMttConfig.mttApi;
            } catch (error) {
                console.log('Error parsing mtt config', error, process.env.R4_CONFIG!);
                throw error;
            }
        }
        MttMain.init(
            async (id: number, data: any) => redis.store(`rmtt:${id}`, JSON.stringify(data)),
            async (id: number) => {
                const result = await redis.fetch(`rmtt:${id}`);
                if (result) {
                    return JSON.parse(result);
                }
                return null;
            },
        );
    }

    start(userData: MttUserData, action: JobType, tournamentId: number, jobData: any, onMessage: JobDataHandler): Promise<void> {
        return new Promise((resolve, reject) => {
            let ticketId = 0;
            if (jobData.ticketId) {
                ticketId = Number(jobData.ticketId);
            }
            MttMain.run(
                userData.mtt.token,
                action,
                tournamentId,
                ticketId,
                onMessage,
                userData?.user?.nickname,
            )
                .then(resolve)
                .catch(reject);
        });
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    stop(_: JobType): Promise<void> {
        // do nothing
        return Promise.resolve();
    }

    isMtt(): boolean {
        return true;
    }
}

export const RMttAdapter = new RMtt();
