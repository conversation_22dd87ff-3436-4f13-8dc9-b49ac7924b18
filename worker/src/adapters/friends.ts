import { <PERSON><PERSON><PERSON>, JobDataHandler, JobType, logging, PlatformUserData } from "shared";
import { GamePlatformAdapter } from "../models/model";
import { CreateFriendsRoomParams, FriendsMain } from "friends";
import { UnrecoverableError } from "bullmq";

export const FriendsAdapter: GamePlatformAdapter = {
    async init(proxyUrl?: string): Promise<void> {
        const urlConfig = {
            wpkHttpURL: process.env.WPK_URL!,
            wpkGameWsURL: process.env.WPK_URL!.replace('http', 'ws'), // tmp for development
        };
		FriendsMain.init(urlConfig, proxyUrl);
    },
    async start(user: PlatformUserData, jobType: JobType, roomId: number, params: JobData, onMessage: JobDataHandler): Promise<void> {
        if (!params.clubId) {
            logging.error('Friends adapter requires clubId in params', { jobData: params });
            throw new UnrecoverableError('Friends adapter requires clubId');
        }
        await FriendsMain.run(
            user,
            jobType,
            roomId,
            params.clubId,
            params.profileName,
            onMessage,
            params.createRoomParams as CreateFriendsRoomParams,
        );
    },
    async stop(_: string): Promise<void> {
        setTimeout(() => {
            throw new Error("Friends adapter stop timeout reached, forcibly stopping the game.");
        }, 10_000);
        await FriendsMain.stop();
    },
    isMtt: () => false,

};
