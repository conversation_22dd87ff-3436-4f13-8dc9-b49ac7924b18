import { UnrecoverableError } from 'bullmq';
import {
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    JobData,
    logging,
    RoomStartParams,
    PkwUserData,
    TableData,
} from 'shared';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';
import { pkwConfigurations } from './pkwConfigurations';

class PkwAdapterClass implements GamePlatformAdapter {
    constructor(
        private readonly gameId: number = 2,
        private readonly gameMode: GameMode = GameMode.NORMAL,
        private readonly gameType: GameType = GameType.NLHE,
        private readonly defaultParams: {
            extraTimeEnabled?: boolean;
        } = {},
    ) {}

    async init(proxyUrl?: string): Promise<void> {
        await PkwMain.init(proxyUrl, this.gameId, this.gameMode, this.gameType);
    }

    async start(
        userData: PkwUserData,
        jobType: JobType,
        tableId: number,
        params: JobData,
        onMessage: JobDataHandler,
    ): Promise<void> {
        if (jobType !== JobType.SCAN && jobType !== JobType.PLAY) {
            throw new UnrecoverableError(`Unsupported game platform action: ${jobType}`);
        }

        if (jobType === JobType.SCAN) {
            const { gameMode, currencies } = pkwConfigurations[params.appId];
            const tablesDataHandler = (tables: TableData[]) => {
                tables = tables.filter((table) => {
                    return (
                        table.gameId === this.gameId &&
                        table.gameMode === gameMode &&
                        currencies.includes(table.currency)
                    );
                });
                onMessage({ tables: tables.map((t) => ({ ...t, appId: params.appId })) });
            };
            return PkwMain.pkwScan(userData, jobType, tablesDataHandler);
        }

        const botParams: RoomStartParams = {
            buyInMultiplier: params.buyInMultiplier ?? 100,
            rebuyEnabled: params.rebuyEnabled ?? true,
            rebuyThreshold: params.rebuyThreshold ?? 50,
            maxRebuyCount: params.maxRebuyCount ?? 100,
            withdrawAmount: params.withdrawAmount ?? 0,
            withdrawThreshold: params.withdrawThreshold ?? 0,
            profileName: params.profileName,
            extraTimeEnabled: this.defaultParams.extraTimeEnabled ?? false,
        };

        return PkwMain.pkwPlay(userData, jobType, tableId, botParams, onMessage);
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Adapter] stop', jobType);
        if (jobType == JobType.PLAY) {
            await PkwMain.finishGame();
        }
    }

    isMtt(): boolean {
        return false;
    }
}

export const CashAdapter = new PkwAdapterClass(2, GameMode.NORMAL, GameType.NLHE, {
    extraTimeEnabled: true,
});
export const DiamondAdapter = new PkwAdapterClass(2, GameMode.NORMAL, GameType.NLHE, {
    extraTimeEnabled: true,
});
export const SplashAdapter = new PkwAdapterClass(60, GameMode.SPLASH);
export const SplashDiamondAdapter = new PkwAdapterClass(60, GameMode.SPLASH);
export const ZoomAdapter = new PkwAdapterClass(40, GameMode.ZOOM);
export const ShortDeckAdapter = new PkwAdapterClass(2, GameMode.NORMAL, GameType.SHORTDECK);
