import Fastify, { FastifyReply, FastifyRequest } from 'fastify';
import FastifyExpress from '@fastify/express';
import { FastifyAdapter } from '@bull-board/fastify';

import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';

import prometheusClient from 'prom-client';

import { addUserAccountsAndPlayers } from '../controller';
import { UserAccountRequestBody } from '../models/api';
import { getUserAccount } from '../mongo';

import { PORT } from '../config';
import { version } from '../../package.json';
import redis from '../redis';

export async function initFastify(arenaInstance: any) {
    // 1. Creating Fastify app and adding basic routes
    const fastify = Fastify();
    fastify.get('/health', async () => {
        return { status: 'ok' };
    });

    fastify.get('/version', async () => {
        return { version };
    });

    fastify.get('/metrics', async (_, reply) => {
        reply.header('Content-Type', prometheusClient.register.contentType);
        return prometheusClient.register.metrics();
    });

    fastify.get(
        '/user_id',
        async (request: FastifyRequest<{ Querystring: { player_id: string } }>, reply: FastifyReply) => {
            const player_id = request.query.player_id;
            if (!player_id) {
                return reply.status(400).send({ message: 'Invalid request - player_id is required' });
            }
            try {
                const userAccount = await getUserAccount(player_id);
                if (!userAccount) {
                    return reply.status(404).send({ message: 'User account not found' });
                }
                const { userId } = userAccount;
                return reply.status(200).send({ userId });
            } catch (error: Error | any) {
                return reply.status(500).send({ message: 'Server error', error: error.message });
            }
        },
    );

    fastify.post(
        '/user_accounts',
        async (request: FastifyRequest<{ Body: UserAccountRequestBody }>, reply: FastifyReply) => {
            if (!request.body) {
                return reply.status(400).send({ status: 400, message: 'Invalid request' });
            }
            try {
                const playerIds = await addUserAccountsAndPlayers(request.body);
                const payload = { status: 200, message: 'User accounts added', players: playerIds };
                return reply.status(200).send(payload);
            } catch (error: Error | any) {
                return reply.status(500).send({ status: 500, message: 'Server error', error: error.message });
            }
        },
    );

    // 2. Adding Fastify BullMQ adapter
    const serverAdapter = new FastifyAdapter();
    serverAdapter.setBasePath('/dashboard');
    createBullBoard({
        queues: [new BullMQAdapter(redis.workerQueue)],
        serverAdapter,
    });

    fastify.register(serverAdapter.registerPlugin(), {
        prefix: '/dashboard',
    });

    // 3. Fastify-express
    try {
        await fastify.register(FastifyExpress);
        fastify.use('/arena', (req, res, next) => {
            res.setHeader('Content-Type', 'text/html');
            arenaInstance(req, res, next);
        });

        await fastify.listen({ port: PORT, host: '0.0.0.0' });
        console.log(`Server listening on ${PORT}`);
    } catch (err) {
        console.log(`Error starting server: ${err}`);
        fastify.log.error(err);
        process.exit(1);
    }
}
