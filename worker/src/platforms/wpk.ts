import { PlatformStrategy } from './platform-strategy';
import { EncryptUtils, PkwMain, RobotHttpReq } from 'pkw';
import { TransferJobData } from '../models/model';
import { JobType, logging, MttUserData, PkwUserData, PlatformUserData, Balance, UserData } from 'shared';
import { CurrencyType } from '../types';
import redis from '../redis';
import { UnrecoverableError } from 'bullmq';


const IS_NOT_PHONE_LOGIN = false;
const IS_SIGNED = true;
const IS_NOT_SIGNED = false;
const TRANSFER_PASSWORD = '576432';

class WpkStrategy implements PlatformStrategy {
    proxyUrl?: string;
    cacheKey: string = 'user:wpk';

    init(proxyUrl?: string) {
        this.proxyUrl = proxyUrl;
    }

    async login(user: UserData): Promise<PkwUserData> {
        let userData: any;
        const cachedLoginData = await redis.fetch(`${this.cacheKey}:${user.userId}`);
        if (cachedLoginData) {
            userData = JSON.parse(cachedLoginData);
        } else {
            const response = await RobotHttpReq.wpkLogin(user, IS_NOT_PHONE_LOGIN, IS_NOT_SIGNED, this.proxyUrl);
            await redis.store(`${this.cacheKey}:${user.userId}`, JSON.stringify(response), 60 * 60 * 8); // Cache for 8 hours
            userData = response;
        }

        return {
            userId: userData.user.userId,
            token: userData.sessionToken,
            deviceId: user.deviceId,
            pkwAuthData: userData.pkwAuthData,
            user: {
                nickname: userData.user.nickname,
            },
        };
    }

    async fetchMttUserData(platformUser: PlatformUserData): Promise<MttUserData> {
        await PkwMain.init(this.proxyUrl);
        const pkwUser = platformUser as PkwUserData;
        const loginData = await PkwMain.pkwLogin(pkwUser, JobType.LOGIN);
        const data: MttUserData = {
            mtt: { token: loginData?.mtt?.token },
            user: { nickname: pkwUser.user.nickname! }
        };

        PkwMain.closeWebSocket();
        return data;
    }

    async clearCache(userId: number) {
        await redis.remove(`${this.cacheKey}:${userId}`);
    }

    async balance(platformUser: PlatformUserData): Promise<Balance> {
        const [diamondBalance, walletInfo] = await Promise.all([
            // Fetch diamond balance
            this.wpkRequest(platformUser, '/deal/getBalanceDiamond'),
            // Fetch wallet balance
            this.wpkRequest(platformUser, '/userWallet/getUserWalletInfo'),
        ]);

        // Validate the responses
        if (diamondBalance?.data?.balanceDiamond == null) {
            throw new Error('Invalid diamond balance response');
        }
        if (walletInfo?.result?.Data?.Amount == null || walletInfo?.result?.Data?.USDTAmount == null) {
            throw new Error('Invalid wallet info response');
        }

        return {
            diamond: diamondBalance.data.balanceDiamond,
            gold: walletInfo.result.Data.Amount,
            usdt: walletInfo.result.Data.USDTAmount,
        } as Balance;
    }

    async transfer(platformUser: PlatformUserData, jobData: TransferJobData): Promise<void> {
        // to be able to transfer, we need to change the payment password first
        const newPass = await this.changePassword(platformUser);
        const params: Record<string, any> = {
            receiverId: jobData.receiverId,
            type: 0,
            passwd: newPass,
            friendType: 0,
        };

        if (jobData.currency !== CurrencyType.DIAMOND && jobData.currency !== CurrencyType.GOLD) {
            throw new Error(`Unsupported currency for WPK transfer: ${jobData.currency}`);
        }

        let transferEndpoint: string;
        if (jobData.currency === CurrencyType.DIAMOND) {
            transferEndpoint = '/diamond/give';
            params.diamondNum = jobData.transferAmount;
            logging.info('Transferring DIAMOND:', jobData.transferAmount);
        } else {
            transferEndpoint = '/gold/give';
            params.num = jobData.transferAmount;
            logging.info('Transferring GOLD:', jobData.transferAmount);
        }

        const response = await this.wpkRequest(platformUser, transferEndpoint, params);
        if (response?.errorCode === 0) {
            logging.info('Transfer successful:', response);
        } else {
            logging.error('Transfer error:', response?.errMsg, response);
            throw new UnrecoverableError(`Transfer failed: ${response?.errMsg || 'Unknown error'}`);
        }
    }

    async changePassword(platformUser: PlatformUserData): Promise<string> {
        const newPwdStr = EncryptUtils.MD5(TRANSFER_PASSWORD + RobotHttpReq.STATIC_SALT).toUpperCase();
        logging.info('changePassword', newPwdStr);
        const params = {
            oldPwd: '',
            pwd: newPwdStr,
        };

        const response = await this.wpkRequest(platformUser, '/userWallet/setUserPwd', params);
        if (response?.errorCode === 0) {
            logging.info('changePassword successful:', response);
        } else {
            logging.warn('changePassword error', response);
        }
        // return the new password string even if the change was not successful
        return newPwdStr;
    }

    async wpkRequest(platformUser: PlatformUserData, url: string, params: any = {}): Promise<any> {
        return RobotHttpReq.wpkRequest(platformUser, url, {}, IS_SIGNED, this.proxyUrl, params);
    }
}

export const WpkPlatform = new WpkStrategy();
