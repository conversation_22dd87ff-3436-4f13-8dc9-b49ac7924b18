import { RobotHttpReq } from 'pkw';

import { MttUserData, Balance, UserData, PlatformUserData, PkwUserData } from 'shared';
import { PlatformStrategy } from './platform-strategy';

const IS_PHONE_LOGIN = true;
const IS_SIGNED = true;

class RWpkStrategy implements PlatformStrategy {
    init() {
    }

    async login(user: UserData): Promise<PkwUserData> {
        return RobotHttpReq.wpkLogin(user, IS_PHONE_LOGIN, IS_SIGNED);
    }

    async fetchMttUserData(platformUser: PlatformUserData): Promise<MttUserData> {
        const response = await RobotHttpReq.wpkRequest(platformUser, '/mtt/authApi', {}, true);
        const nickname = (platformUser as PkwUserData).user.nickname!;
        return { mtt: { token: response.data.token}, user: { nickname } };
    }

    async balance(platformUser: PlatformUserData) {
        const response: any = await <PERSON>HttpReq.wpkRequest(platformUser, '/userWallet/getWalletPageInfo', {}, IS_SIGNED);
        if (response?.data?.walletInfo?.amount == null) {
            throw new Error('Invalid wallet response format');
        }
        const wallet = response.data.walletInfo;

        return {
            diamond: 0,
            gold: wallet.amount,
            usdt: 0,
        } as Balance;
    }

    async clearCache() {
        throw new Error('Method not implemented.');
    }

    async transfer() {
        throw new Error('Method not implemented.');
    }
}

export const RWpkPlatform = new RWpkStrategy();
