import { <PERSON>T<PERSON>, <PERSON>D<PERSON>, JobDataHandler } from 'shared';
import { CurrencyType } from '../types';

export interface TransferJobData extends JobData {
    currency: CurrencyType;
    transferAmount: number; // amount to transfer to receiver
    receiverId: number; // userId of the receiver for transfer
    receiverUsername?: string; // username of the receiver for transfer
}

export interface GamePlatformAdapter {
    init(proxyUrl?: string): Promise<void>;
    start(user: any, action: JobType, tableId: number, params: JobData, onMessage: JobDataHandler): Promise<void>;
    stop(action: JobType): Promise<void>;
    isMtt(): boolean;
}
