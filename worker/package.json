{"name": "worker", "version": "0.30.29", "main": "index.js", "scripts": {"start:ts": "ts-node src/index.ts", "start": "npm run build && cross-env NODE_ENV=local node --enable-source-maps dist/src/index.js", "start:dev": "npm run build && cross-env NODE_ENV=dev node --enable-source-maps dist/src/index.js", "start:local": "npm run build && cross-env NODE_ENV=local node --enable-source-maps dist/src/local.js", "start:local:ts": "cross-env NODE_ENV=local ts-node src/local.ts", "start:local:mtt": "npm run build:mtt && npm run start:local", "start:local:rmtt": "npm run build:rmtt && npm run start:local", "test": "tsx --test", "test:all": "npm run build:all && npm run test && npm run test:libs", "test:glue": "NODE_ENV=local GLUE_TEST=1 SESSIONS=500 ts-node tests/integration/glue-test.ts", "test:job": "REDIS_CLUSTER_MODE=false ts-node ./tasks/add_mq_job.ts", "test:libs": "npm run test --workspaces", "test:docker:build": "npm run clean && npm run docker:build", "docker:build": "docker build . -t worker", "clean": "rm -rf node_modules && rm -rf **/*/node_modules && rm -rf dist && rm -rf **/*/dist", "build": "tsc", "build:all": "npm run build:libs && npm run build", "build:friends": "npm run build --workspace=libs/friends", "build:shared": "npm run build --workspace=libs/shared", "build:pkw": "npm run build --workspace=libs/pkw", "build:mtt": "npm run build --workspace=libs/mtt", "build:rmtt": "npm run build --workspace=libs/rmtt", "build:wptgo": "npm run build --workspace=libs/wptgo", "build:libs": "npm run build --workspaces"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/express": "^5.0.1", "@types/node": "^22.15.3", "@types/node-fetch": "^2.6.12", "cross-env": "^7.0.3", "eslint": "^9.25.1", "globals": "^16.0.0", "ncp": "^2.0.0", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.1"}, "dependencies": {"@bull-board/api": "^6.9.2", "@bull-board/fastify": "^6.9.2", "@fastify/express": "^4.0.2", "bull-arena": "^4.5.1", "bullmq": "^5.51.1", "dotenv": "^17.2.0", "fastify": "^5.3.2", "fernet": "^0.3.3", "friends": "file:libs/friends", "https-proxy-agent": "^7.0.6", "ioredis": "^5.6.1", "mongoose": "^8.14.0", "mtt": "file:libs/mtt", "node-fetch": "^3.3.2", "pkw": "file:libs/pkw", "prom-client": "^15.1.3", "redlock": "^5.0.0-beta.2", "rmtt": "file:libs/rmtt", "shared": "file:libs/shared", "tsx": "^4.20.3", "uuid": "^11.1.0", "wptgo": "file:libs/wptgo", "ws": "^8.18.3"}, "workspaces": ["libs/shared", "libs/pkw", "libs/mtt", "libs/rmtt", "libs/wptgo", "libs/friends"]}