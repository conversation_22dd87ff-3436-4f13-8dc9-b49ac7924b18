# Use the official Python 3.12 image as the base image
FROM python:3.12

# Set the environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Set the working directory inside the container
WORKDIR /app

# Copy the poetry.lock and pyproject.toml files to the working directory
COPY poetry.lock pyproject.toml /app/

# Install Poetry
RUN pip install poetry

# Install project dependencies
RUN poetry install --no-root --without dev

# Copy the rest of the application code to the working directory
COPY /src ./src

# Expose the port that the FastAPI application will run on
EXPOSE 8888

# Start the FastAPI application
COPY --chmod=100 uvicorn_starter.sh .
COPY uvicorn_logs.yaml .
CMD ["./uvicorn_starter.sh"]

