environment: &environment prod

service:
  - type: ClusterIP
    port: 8888
    targetPort: 8888

configmap:
  platforms:
    98:
      name: WPK
      games: [81, 82, 83, 85, 86, 87, 89]
    101:
      name: WPTGO
      games: [81, 83, 85, 87, 89]
  CLUBS: []
  IP_POOL_ENABLED: "True"
  MAX_PLAYERS_PER_IP: "1"
  WPK_URL: "http://************:8888"
  WPTGO_URL: "https://player-creator.prod.wptg.kashxa-infra.com"
  WPK_SIGN_SALT: "wS4CBakx"
  JOB_STALL_TIMEOUT: 3600
  UNLEASH_API_URL: "https://unleashedge.fungamer.io/api"
  DISABLE_IP_POOL_APP_IDS_CHECK: "False"

deployment:
  image:
    repository: 124355651851.dkr.ecr.ap-southeast-1.amazonaws.com/fg/botmanager
    tag: 0.15.56
  replicaCount: 2
  revisionHistoryLimitCount: 5
  nodePoolTolerationValue: resource-intensive
  resources:
    limits:
      cpu: 4
      memory: "8192Mi"
    requests:
      cpu: "3548m"
      memory: "7168Mi"

secrets:
  - type: ESO
    name: wptgo-token
    secrets:
      WPTGO_TOKEN: bots/svc.botmanager.wptgo_token

ingress:
  - ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    rules:
      - host: botmanager.fungamer.io
        paths:
          - path: /api
            pathType: Prefix
            number: 8888
