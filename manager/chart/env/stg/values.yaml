environment: &environment stg

service:
  - type: ClusterIP
    port: 8888
    targetPort: 8888

configmap:
  platforms:
    94:
      name: RWPK
      games: [84]
    98:
      name: WPK
      games: [69, 81, 82, 83, 85, 86, 87, 89]
    101:
      name: WPTGO
      games: [81, 83, 85, 87, 89]
  CLUBS: []
  IP_POOL_ENABLED: "True"
  MAX_PLAYERS_PER_IP: "10"
  WPK_URL: "http://**************:83"
  WPTGO_URL: "http://*************:7170"
  WPK_SIGN_SALT: "WAv1yNGx"
  JOB_STALL_TIMEOUT: 3600
  UNLEASH_API_URL: "https://unleashedge.stg.fungamer.io/api"
  DISABLE_IP_POOL_APP_IDS_CHECK: "False"

deployment:
  image:
    repository: 124355651851.dkr.ecr.ap-southeast-1.amazonaws.com/fg/botmanager
    tag: 0.15.56-stg
  replicaCount: 1
  revisionHistoryLimitCount: 5
  nodePoolTolerationValue: default
  resources:
    limits:
      cpu: "1024m"
      memory: "2048Mi"
    requests:
      cpu: "512m"
      memory: "1024Mi"

secrets:
  - type: ESO
    name: wptgo-token
    secrets:
      WPTGO_TOKEN: bots/svc.botmanager.wptgo_token

ingress:
  - ingressClassName: nginx
    annotations:
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    rules:
      - host: botmanager.stg.fungamer.io
        paths:
          - path: /api
            pathType: Prefix
            number: 8888
