# Changelog

### 0.15.58-dev
- patch: fix crash
- docs: Update helm chart and changelog

### 0.15.57-dev
- patch: do not stop bots that just started
- docs: Update helm chart and changelog

### 0.15.56
- docs: Update helm chart and changelog

### 0.15.56-stg
- docs: Update helm chart and changelog

### 0.15.56-dev
- patch: don't delete finished job when starting new job. Don't create Launch until job successfully started
- docs: Update helm chart and changelog

### 0.15.55
- docs: Update helm chart and changelog

### 0.15.55-stg
- docs: Update helm chart and changelog

### 0.15.55-dev
- patch: improve player checks in stop automation logic
- docs: Update helm chart and changelog

### 0.15.54
- docs: Update helm chart and changelog

### 0.15.54-stg
- docs: Update helm chart and changelog

### 0.15.54-dev
- patch: add bot_type to player response, enhance automation logging for player checks
- docs: Update helm chart and changelog

### 0.15.53
- docs: Update helm chart and changelog

### 0.15.53-stg
- docs: Update helm chart and changelog

### 0.15.53-dev
- patch: clamp cover level to valid range in overlay filler
- docs: Update helm chart and changelog

### 0.15.52
- docs: Update helm chart and changelog

### 0.15.52-stg
- docs: Update helm chart and changelog

### 0.15.52-dev
- patch: fix bot type comparison in active players query
- docs: Update helm chart and changelog

### 0.15.51
- docs: Update helm chart and changelog

### 0.15.51-stg
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.15.51-dev
- patch: optimize db query when auto adjusting game pools

### 0.15.49-dev
- patch: fix scan  error for clubs scan bot
- docs: Update helm chart and changelog

### 0.15.48-dev
- patch: change exception type for tournament checks
- docs: Update helm chart and changelog

### 0.15.47-dev
- patch: raise ValueError instead of Exception when multiflight id not found
- docs: Update helm chart and changelog

### 0.15.46
- docs: Update helm chart and changelog

### 0.15.46-stg
- docs: Update helm chart and changelog

### 0.15.46-dev
- patch: simplify max bots amount calculation for mtt overlay filler
- patch: auto-adjust game pool to 10 for tournaments with game pool < 600$
- patch: add check of reg fee for 10k$ tournaments
- docs: Update helm chart and changelog

### 0.15.45-dev
- patch: mtt threshold for 10k usd changed to 35%
- docs: Update helm chart and changelog

### 0.15.44-dev
- patch: remove limitations on satellite tournaments
- docs: Update helm chart and changelog

### 0.15.43
- docs: Update helm chart and changelog

### 0.15.43-stg
- docs: Update helm chart and changelog

### 0.15.43-dev
- patch: add request and response fields for zoom_allowed_bots_percent tables automation param
- docs: Update helm chart and changelog

### 0.15.42-dev
- patch: for zoom tables automation use wider max hands played range
- patch: for zoom tables automation, use percentage of bots instead of total count
- docs: Update helm chart and changelog

### 0.15.41
- docs: Update helm chart and changelog

### 0.15.41-stg
- docs: Update helm chart and changelog

### 0.15.41-dev
- patch: User defined RequestValidationError handling
- docs: Update helm chart and changelog

### 0.15.40-dev
- patch: add option for multiple reentries to the tournament in overlay filler
- linter fix
- patch: overlay filler refactoring - don't pass final_tournament_id through chain of methods
- docs: Update helm chart and changelog

### 0.15.39-stg
- docs: Update helm chart and changelog

### 0.15.39-dev
- patch: add tables_affected_percentage field to TablesAutomationConfigResponse
- docs: Update helm chart and changelog

### 0.15.38-dev
- patch: add table automation config value to start bots only on part of tables filtered by table_id
- docs: Update helm chart and changelog

### 0.15.37-dev
- patch: add club_id param to table automation config requests
- docs: Update helm chart and changelog

### 0.15.36-dev
- patch: enable tables automation for friends
- docs: Update helm chart and changelog

### 0.15.35
- docs: Update helm chart and changelog

### 0.15.35-stg
- docs: Update helm chart and changelog

### 0.15.35-dev
- patch: ARMS-303 disable player if bot finishes with status `suspended`
- patch: ARMS-303 disable player if error message contains pkw specific suspended text
- docs: Update helm chart and changelog

### 0.15.34
- docs: Update helm chart and changelog

### 0.15.34-stg
- docs: Update helm chart and changelog

### 0.15.34-dev
- patch: add validation for scheduling range in update tournament config requests
- docs: Update helm chart and changelog

### 0.15.33-dev
- patch: add custom delay support for tournament bot scheduling
- docs: Update helm chart and changelog

### 0.15.32-dev
- patch: await for starting scan bots
- TOP-498 Removing endpoint to ingress /clubs
- docs: Update helm chart and changelog

### 0.15.31-dev
- patch: do not allow overlay filler for late registration tournaments before tournament start
- TOP-498 Adding new endpoint to ingress /clubs
- TOP-498 Adding new endpoint to ingress /clubs
- docs: Update helm chart and changelog

### 0.15.30-dev
- patch: remove club tables from db when they are removed from game
- docs: Update helm chart and changelog

### 0.15.29-dev
- patch: clean up list of friends tables when receiving empty list
- docs: Update helm chart and changelog

### 0.15.28
- docs: Update helm chart and changelog

### 0.15.28-stg
- docs: Update helm chart and changelog

### 0.15.28-dev
- patch: for mtt overlay filler clamp desired coverage to 1 instead of asserting its value
- docs: Update helm chart and changelog

### 0.15.27-dev
- patch: change check for max_rebuy_count in tables automation
- patch: send max rebuy count to worker data
- docs: disable clubs in env for staging and prod
- docs: Update helm chart and changelog

### 0.15.26-dev
- patch: add club_id to bot start request
- docs: Update helm chart and changelog

### 0.15.25
- docs: Update helm chart and changelog

### 0.15.25-stg
- docs: Update helm chart and changelog

### 0.15.25-dev
- patch: add clubId to jobData for friends tables
- docs: Update helm chart and changelog

### 0.15.24-dev
- patch: fix update of tables with empty list
- docs: Update helm chart and changelog

### 0.15.23-dev
- patch: fix stopping tables automation bots. Don't stop bot if table not found - maybe it's in sync
- docs: Update helm chart and changelog

### 0.15.22
- docs: Update helm chart and changelog

### 0.15.22-stg
- docs: Update helm chart and changelog

### 0.15.22-dev
- patch: improve job removal error handling
- docs: Update helm chart and changelog

### 0.15.21-dev
- patch: pick random players to start bot for tables automation and tournaments overlay
- docs: Update helm chart and changelog

### 0.15.20-dev
- patch: list club tables by club id
- docs: Update helm chart and changelog

### 0.15.19
- docs: Update helm chart and changelog

### 0.15.19-stg
- docs: Update helm chart and changelog

### 0.15.19-dev
- patch: make club_id optional in list tables endpoint
- docs: Update helm chart and changelog

### 0.15.18-dev
- patch: check if scan bot is already running for a club
- docs: Update helm chart and changelog

### 0.15.17
- docs: Update helm chart and changelog

### 0.15.17-stg
- docs: Update helm chart and changelog

### 0.15.17-dev
- patch: add clubs API, add support for friends scan bots, add club_ids to player model, save friends tables data in db
- docs: Update helm chart and changelog

### 0.15.16-dev
- patch: Avatar url added to Player
- docs: Update helm chart and changelog

### 0.15.15-dev
- patch: Change avatar error fix
- docs: Update helm chart and changelog

### 0.15.14-dev
- patch: Feature/avatar-changed flag introduced
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: New field for registration verification_type
- docs: Update helm chart and changelog
- patch: Do not send area_code and phone_number if they are none at all
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: Send nulls for phione number and area_code if email provided
- docs: Update helm chart and changelog
- patch: add friends apptype to WPK platform on local, dev and stg
- docs: Update helm chart and changelog
- patch: add API params to pass create friends table params to worker
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: fix override of tournament adjusted game pool

### 0.15.13
- docs: Update helm chart and changelog

### 0.15.13-stg
- docs: Update helm chart and changelog

### 0.15.13-dev
- patch: New field for registration verification_type
- docs: Update helm chart and changelog

### 0.15.12-dev
- patch: Do not send area_code and phone_number if they are none at all
- docs: Update helm chart and changelog

### 0.15.11
- docs: Update helm chart and changelog

### 0.15.11-stg
- docs: Update helm chart and changelog

### 0.15.11-dev
- patch: Send nulls for phione number and area_code if email provided
- docs: Update helm chart and changelog

### 0.15.10-dev
- patch: add friends apptype to WPK platform on local, dev and stg
- docs: Update helm chart and changelog

### 0.15.9-dev
- patch: add API params to pass create friends table params to worker
- docs: Update helm chart and changelog

### 0.15.8
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: fix override of tournament adjusted game pool

### 0.15.8-stg
- init

### 0.15.8-dev
- patch: fix override of tournament adjusted game pool
- docs: Update helm chart and changelog

### 0.15.7
- docs: Update helm chart and changelog

### 0.15.7-stg
- docs: Update helm chart and changelog

### 0.15.7-dev
- patch: remove bot-manager-interfaces

### 0.15.2
- docs: Update helm chart and changelog

### 0.15.2-stg
- docs: Update helm chart and changelog

### 0.15.2-dev
- patch: Strategy profile types and migration refactor
- docs: Update helm chart and changelog

### 0.15.1
- docs: Update helm chart and changelog

### 0.15.1-stg
- docs: Update helm chart and changelog

### 0.14.14
- docs: Update helm chart and changelog

### 0.15.1-dev
- patch: strategy profile migration fix
- docs: Update helm chart and changelog

### 0.15.0-dev
- minor: Feature/percentage strategy
- docs: Update helm chart and changelog

### 0.14.14-stg
- docs: Update helm chart and changelog

### 0.14.14-dev
- patch: don't stop just started players in tables automation
- docs: Update helm chart and changelog

### 0.14.13-dev
- patch: fix Seat serialization
- docs: Update helm chart and changelog

### 0.14.12-stg
- docs: Update helm chart and changelog
- patch: avatar change endpoint added

### 0.14.12-dev
- patch: avatar change endpoint added

### 0.14.11-dev
- patch: small refactoring for overlay filler. Make feature flags test fast
- docs: Update helm chart and changelog

### 0.14.10-dev
- patch: chane bot ids.
- PLAY TRANSFER and BALANCE now have platform_id instead of app_id
- change job ids to use platform
- add table.app_id to manual start bot request, add app_id to Seat model
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.14.9-dev
- patch: add players count to stop for tables automation
- Approved-by: Alexandr Maximov

### 0.14.8-dev
- patch: add allowed_games to players list response
- docs: Update helm chart and changelog

### 0.14.7-dev
- patch: Scan job - now can process multiple AppIds
- docs: Update helm chart and changelog

### 0.14.6-dev
- patch: make Launch.config_id have default value
- docs: Update helm chart and changelog

### 0.14.5
- docs: Update helm chart and changelog

### 0.14.5-stg
- docs: Update helm chart and changelog

### 0.14.5-dev
- patch: fix propagation username to worker
- docs: Update helm chart and changelog

### 0.14.4-dev
- patch: do not allow players booked in upcoming tournaments to play on table games
- this was not possible before because players would never interact, but now possible if player.allowed_games = [81, 83] for example. Here we prevent this
- docs: Update helm chart and changelog

### 0.14.3
- docs: Update helm chart and changelog

### 0.14.3-stg
- docs: Update helm chart and changelog

### 0.14.3-dev
- patch: add user_name field to wptgo registration data
- docs: Update helm chart and changelog

### 0.14.2-stg
- docs: Update helm chart and changelog

### 0.14.2-dev
- patch: add allowed_games to update_player request model
- docs: Update helm chart and changelog

### 0.14.1-dev
- patch: fix migration tasks
- docs: Update helm chart and changelog

### 0.14.0-dev
- lint
- cleanup
- add test for unique proxy for players on the table for automation
- add allowed_games to player
- minor: tables automation should not use player.app_id for searching for free players. Players API now returns all players for non-tournaments
- minor: tables automation should not use player.app_id for searching for free players. Players API now returns all players for non-tournaments
- docs: Update helm chart and changelog

### 0.13.16-dev
- patch: enable buyout for tables automation for gold and usd tables
- docs: Update helm chart and changelog

### 0.13.15-dev
- patch: 422 error handling
- docs: Update helm chart and changelog

### 0.13.14-dev
- patch: fix scanning bot
- docs: Update helm chart and changelog

### 0.13.13-dev
- patch: remove App, Platform and PlatformGame from db. Platforms and Apps for them are now set through env variables
- docs: Update helm chart and changelog

### 0.13.12-dev
- patch: strategic profile introduced in tables automation
- docs: Update helm chart and changelog

### 0.13.11-dev
- patch: remove separate switches for enabling IP Pools per app_id
- docs: Update helm chart and changelog

### 0.13.10-dev
- patch: optimize db queries and multiflight task
- query only needed players for tables request
- fix migrations task
- query only one random player for scanning bot
- use smaller models for multiflight check task
- docs: Update helm chart and changelog

### 0.13.9
- docs: Update helm chart and changelog

### 0.13.9-stg
- docs: Update helm chart and changelog

### 0.13.9-dev
- patch: add endpoint for multiflight tournament checks

### 0.13.8-dev
- patch: don't use outdated launches for calculating number of bots added to tournament
- docs: Update helm chart and changelog

### 0.13.7
- docs: Update helm chart and changelog

### 0.13.7-stg
- docs: Update helm chart and changelog

### 0.13.7-dev
- patch: make Launch.config_id optional for migrations. Remove old Launches
- docs: Update helm chart and changelog

### 0.13.6
- docs: Update helm chart and changelog

### 0.13.6-stg
- docs: Update helm chart and changelog

### 0.13.6-dev
- Merge branch 'main' of https://bitbucket.org/aceguardian/manager
- patch: new deploy
- docs: Update helm chart and changelog
- Merge branch 'main' of https://bitbucket.org/aceguardian/manager
- finish_all_old_unifinished_launches fix

### 0.13.5-dev
- patch: Platform based receiver_id handling
- docs: Update helm chart and changelog

### 0.13.4
- docs: Update helm chart and changelog

### 0.13.4-stg
- docs: Update helm chart and changelog

### 0.13.4-dev
- patch: fix multiflight config check task
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog
- patch: disable unused auth for bots api
- docs: Update helm chart and changelog
- patch: remove old FutureTournamentChecks
- docs: Update helm chart and changelog
- patch: fixed strategy profile naming

### 0.13.0
- docs: Update helm chart and changelog
- patch: disable unused auth for bots api
- docs: Update helm chart and changelog
- patch: remove old FutureTournamentChecks
- docs: Update helm chart and changelog
- patch: fixed strategy profile naming

### 0.13.3-dev
- patch: disable unused auth for bots api
- docs: Update helm chart and changelog
- patch: remove old FutureTournamentChecks
- docs: Update helm chart and changelog
- patch: fixed strategy profile naming

### 0.13.2-dev
- patch: remove old FutureTournamentChecks
- docs: Update helm chart and changelog

### 0.13.1-dev
- patch: fixed strategy profile naming
- docs: Update helm chart and changelog

### 0.13.0-stg
- docs: Update helm chart and changelog

### 0.13.0-dev
- minor: Feature/strategy profile
- docs: Update helm chart and changelog

### 0.12.7-dev
- patch: registration fixes
- docs: Update helm chart and changelog

### 0.12.6-dev
- patch: refactor Seat, instead of app_id and player_id use player
- docs: Update helm chart and changelog

### 0.12.5
- docs: Update helm chart and changelog

### 0.12.5-stg
- docs: Update helm chart and changelog

### 0.12.5-dev
- patch: expect scan bots to send app id for each tournament and table, instead of relying on bot's params
- docs: Update helm chart and changelog

### 0.12.4-dev
- patch: fix connection string
- docs: Update helm chart and changelog

### 0.12.3-dev
- patch: create missing db models on app initialization
- docs: Update helm chart and changelog

### 0.12.2-dev
- patch: add currency field to transfer request
- patch: actually pass the currency to transfer bot
- docs: Update helm chart and changelog

### 0.12.1-dev
- patch: do not fail get_player_update_from_active_job when play bot sends tables info
- docs: Update helm chart and changelog

### 0.12.0-dev
- minor: add PlatformGame database model for selecting what games are enabled on what platform
- docs: Update helm chart and changelog

### 0.11.11-dev
- patch: add validation to platform_id by enum. Create platform RWPK by migration
- docs: Update helm chart and changelog

### 0.11.10-dev
- patch: disable-app-id-check-for-ippool setting added
- docs: Update helm chart and changelog

### 0.11.9
- docs: Update helm chart and changelog

### 0.11.9-stg
- docs: Update helm chart and changelog

### 0.11.9-dev
- patch: player_id and user_id returned from the register method
- docs: Update helm chart and changelog

### 0.11.8-dev
- patch: switch from returning ErrorResponse from start_bot and stop_bot to raising and handling exceptions
- query all players for future launches upfront
- validate future launces before bot start
- docs: Update helm chart and changelog

### 0.11.7-dev
- patch: Assign IP with country mapping
- docs: Update helm chart and changelog

### 0.11.6-dev
- patch: Flatten extra_data in PendingRegistrationResponse
- docs: Update helm chart and changelog

### 0.11.5
- docs: Update helm chart and changelog

### 0.11.5-stg
- docs: Update helm chart and changelog

### 0.11.5-dev
- patch: restore bot_id that got overwritten, when receiving job progress
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.11.4-dev
- patch: stop bots in tables automation even if bot_id somehow lost
- docs: Update helm chart and changelog

### 0.11.3-dev
- patch: select scanning bot randomly
- docs: Update helm chart and changelog

### 0.11.2-dev
- patch: migrate feature flags to Unleash
- add Unleash urls to configmap
- patch: add feature flag context
- docs: Update helm chart and changelog

### 0.11.1
- docs: TOP-427 Adjust replicaCount for prod
- docs: Update helm chart and changelog

### 0.11.1-stg
- docs: Update helm chart and changelog

### 0.11.1-dev
- patch: TOP-427 Update helm chart to align with new library release
- docs: Update helm chart and changelog

### 0.11.0-dev
- minor: registration automation task
- docs: Update helm chart and changelog

### 0.10.4-stg
- docs: Update helm chart and changelog

### 0.10.4-dev
- patch: Fix the bug with skipping transfer diamond start
- docs: Update helm chart and changelog

### 0.10.3-dev
- patch: add missing possible player state to enum
- docs: Update helm chart and changelog

### 0.10.2-dev
- patch: Merged in fix/player_total_calculation_fixed (pull request #363)
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.10.1
- Revert "player count is now used"
- This reverts commit aca527a6889d4a3bd5c321cde83fb829d8f1eea4.
- player count is now used
- docs: Update helm chart and changelog

### 0.10.1-stg
- docs: Update helm chart and changelog

### 0.10.1-dev
- patch: correct terms for searching players to stop in tables automation , mark all outdated launches as finished
- add logging for migration
- tabs to spaces
- docs: Update helm chart and changelog

### 0.10.0-dev
- minor: Pending Registration API
- docs: Update helm chart and changelog

### 0.9.79-dev
- patch: Log the 101 register payload
- docs: Update helm chart and changelog

### 0.9.78-dev
- patch: add to enum player states that can be sent by worker
- docs: Update helm chart and changelog

### 0.9.77-dev
- patch: revert remove dashes from device_id for wptgo
- docs: Update helm chart and changelog

### 0.9.76-dev
- patch: unify querying players for tournaments and tables automation, add tests for overlay filler players search
- docs: Update helm chart and changelog

### 0.9.75-dev
- patch: overlay filler logs cleanup
- docs: Update helm chart and changelog

### 0.9.74
- docs: Update helm chart and changelog

### 0.9.74-stg
- docs: Update helm chart and changelog

### 0.9.74-dev
- patch: fix time delta calculation for tournament updates
- docs: Update helm chart and changelog

### 0.9.73
- docs: update WPTGO_URL prod value
- docs: Update helm chart and changelog

### 0.9.73-stg
- docs: Update helm chart and changelog

### 0.9.73-dev
- patch: enable tables automation for r5+, validate player update status
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.9.72-dev
- patch: remove dashes from device_id for wptgo

### 0.9.71-dev
- patch: registration automation config
- docs: Update helm chart and changelog

### 0.9.70-dev
- patch: optimize tables automation: reduce second query for active Launches
- docs: Update helm chart and changelog

### 0.9.69-dev
- patch: initialized is a valid status for job in player_states update
- docs: Update helm chart and changelog

### 0.9.68-dev
- patch: remove outdated exception handling, cleanup logging

### 0.9.66-dev
- patch: refactor player states, move calculation of player update and balance to separate methods,
- add validate_on_save for player model
- add validation to writing data in player state update
- docs: update WPTGO_URL prod value
- docs: Update helm chart and changelog

### 0.9.65-dev
- patch: cleanup deprecation warning. remove spamming logs from update_tables_and_tournaments
- docs: Update helm chart and changelog

### 0.9.64
- docs: Update helm chart and changelog

### 0.9.64-stg
- docs: Update helm chart and changelog

### 0.9.64-dev
- patch: remove saving of the referral code and extra data
- docs: Update helm chart and changelog

### 0.9.63
- docs: Update helm chart and changelog

### 0.9.63-stg
- docs: Update helm chart and changelog

### 0.9.63-dev
- patch: Add option to receive and send extra optional fields to the registration method for 101
- docs: Update helm chart and changelog

### 0.9.62
- docs: Update helm chart and changelog

### 0.9.62-stg
- docs: Update helm chart and changelog

### 0.9.62-dev
- patch: Log full urls in register methods
- docs: Update helm chart and changelog

### 0.9.61-dev
- patch: for tables automation update only current launch for player, and don't look into older launches
- docs: Update helm chart and changelog

### 0.9.60-dev
- patch: fix check_stuck_players cleaning up pending bots that didnt start for a while
- docs: Update helm chart and changelog

### 0.9.59-dev
- patch: TOP-414 Add WPTGO_TOKEN to prod
- docs: update WPTGO_URL production value
- docs: Update helm chart and changelog

### 0.9.58-dev
- patch: prettify tables automation
- docs: Update helm chart and changelog

### 0.9.57-dev
- patch: fix conditions of starting new table with tables automation
- docs: Update helm chart and changelog

### 0.9.56-dev
- patch: cleanup logging
- docs: Update helm chart and changelog

### 0.9.55-stg
- docs: Update helm chart and changelog

### 0.9.55-dev
- patch: fix db query for stopping bots on tables
- docs: Update helm chart and changelog

### 0.9.54-dev
- patch: move calculation of max hands played in tables automation before start of playing
- Because it was checking stop every minute, probability of getting lower number was growing, so most players played 90-100, and much less players played 100-110
- Also, Yongtao asked to change range to 60-90
- docs: Update helm chart and changelog

### 0.9.53-dev
- patch: make required fields required
- docs: Update helm chart and changelog

### 0.9.52-dev
- patch: new param for tables automation config for limiting adding new bots to empty tables until other tables are full
- docs: Update helm chart and changelog

### 0.9.51
- docs: Update helm chart and changelog

### 0.9.51-stg
- docs: Update helm chart and changelog

### 0.9.51-dev
- patch: filter players to stop by status to include only those currently playing
- docs: Update helm chart and changelog

### 0.9.50
- docs: Update helm chart and changelog

### 0.9.50-stg
- docs: Update helm chart and changelog

### 0.9.50-dev
- patch: remove newline

### 0.9.49-dev
- patch: set default value for should_stop to False in responses model
- docs: Update helm chart and changelog

### 0.9.48
- docs: Update helm chart and changelog

### 0.9.48-stg
- docs: Update helm chart and changelog

### 0.9.48-dev
- patch: improve error handling when retrieving tables data for players
- docs: Update helm chart and changelog

### 0.9.47-dev
- patch: update check_stuck_players task to check all enabled players and adjust check logic
- docs: Update helm chart and changelog

### 0.9.46-dev
- patch: check stuck players update values to empty strings
- docs: Update helm chart and changelog

### 0.9.45-stg
- docs: Update helm chart and changelog

### 0.9.45-dev
- patch: exclude None fields in player updates and use empty strings for reset fields
- docs: Update helm chart and changelog

### 0.9.44-dev
- patch: balance to_response
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.9.43-dev
- patch: make Player.should_stop optional

### 0.9.41
- docs: Update helm chart and changelog

### 0.9.41-dev
- patch: try start scanning job even if bot_id is not empty
- docs: Update helm chart and changelog

### 0.9.40
- docs: Update helm chart and changelog

### 0.9.40-stg
- docs: Update helm chart and changelog

### 0.9.39-dev
- patch: we were not cleaning up bot id and params after bot stop

### 0.9.38-dev
- patch: add missing fields to TablesAutomationConfigResponse
- docs: Update helm chart and changelog

### 0.9.37-dev
- patch: use full bot stop from BotService instead of minimal implementation for player_states task
- docs: Update helm chart and changelog

### 0.9.36-dev
- patch: cleanup logging of repeating ValueError when trying to get empty list of jobs
- docs: Update helm chart and changelog

### 0.9.35-dev
- patch: receiver_id wrong type fix
- docs: Update helm chart and changelog

### 0.9.34
- docs: Update helm chart and changelog

### 0.9.34-stg
- docs: Update helm chart and changelog

### 0.9.34-dev
- patch: Little bit more logging on player_states task
- docs: Update helm chart and changelog

### 0.9.33
- TOP-402 Add UNLEASH_API_TOKEN secret to helm chart
- docs: Update helm chart and changelog

### 0.9.33-stg
- docs: Update helm chart and changelog

### 0.9.33-dev
- patch: add option to configure max bots and max rebuy count for tables automation
- patch: add buy in multipliers for tables automation
- docs: Update helm chart and changelog

### 0.9.32
- docs: Update helm chart and changelog

### 0.9.32-stg
- docs: Update helm chart and changelog

### 0.9.32-dev
- patch: refactor get_matching_tables, improve logging for tables automation
- docs: Update helm chart and changelog

### 0.9.31-dev
- patch: change condition to stop bots for table automation. add logging about play time and results
- docs: Update helm chart and changelog

### 0.9.30-dev
- patch: fix remaining instantiation of services for using of static methods. add tests for launch API
- docs: Update helm chart and changelog

### 0.9.29-dev
- patch: Fix the transfer
- docs: Update helm chart and changelog

### 0.9.28-dev
- patch: New fields for the register method for 101
- docs: Update helm chart and changelog

### 0.9.27-dev
- patch: add R6 game: Splash diamond
- docs: Update helm chart and changelog

### 0.9.26-dev
- patch: enable tables automation for players with USD balance (platform 101)
- docs: Update helm chart and changelog

### 0.9.25-dev
- patch:Routers refactoring to functional style
- Added response wrapper to get rid of Response model for every router
- Removed redundant descriptions and summaries, because OpenAPI generates them based on method name
- Changed some Service methods to static so we can remove FastAPI dependencies
- docs: Update helm chart and changelog

### 0.9.24-dev
- patch: check players stuck in not idle state without active job, reset state to IDLE only for players who were not updated for long time
- docs: Update helm chart and changelog

### 0.9.23-dev
- patch: USD for the new platform in overlay_filler
- docs: Update helm chart and changelog

### 0.9.22
- docs: Update helm chart and changelog

### 0.9.22-stg
- docs: Update helm chart and changelog

### 0.9.22-dev
- patch: WPK register fix
- docs: Update helm chart and changelog

### 0.9.21-stg
- docs: Update helm chart and changelog

### 0.9.21-dev
- patch: add API method to batch update adjusted game pool for tournaments
- remove validation of tournaments existing before creating TournamentConfig
- docs: Update helm chart and changelog

### 0.9.20-stg
- docs: Update helm chart and changelog

### 0.9.20-dev
- patch: fix updating of tables automation config
- docs: Update helm chart and changelog

### 0.9.19-dev
- patch: return tables automation config model
- docs: Update helm chart and changelog

### 0.9.18-dev
- patch: Remove generic register method
- docs: Update helm chart and changelog

### 0.9.17-dev
- patch: fix division by zero
- docs: Update helm chart and changelog

### 0.9.16-dev
- patch: fix game pool calculation for tournaments API
- docs: Update helm chart and changelog

### 0.9.15-dev
- patch: fix tables automation, remove database query which does not work for DocumentDB
- docs: Update helm chart and changelog

### 0.9.14-dev
- patch: update WPTGO platform ID to 101
- docs: Update helm chart and changelog

### 0.9.13-dev
- patch: make some of TournamentResponse fields required and move them into constructor
- docs: Update helm chart and changelog

### 0.9.12
- docs: Update helm chart and changelog

### 0.9.12-stg
- docs: Update helm chart and changelog

### 0.9.12-dev
- patch: fix tables automation config  exception handling, unify building of tournament responses in API
- docs: Update helm chart and changelog

### 0.9.11-dev
- patch: improve logging for overlay filler
- fix line length error
- early return on calculating how many players to add
- docs: Update helm chart and changelog

### 0.9.10-dev
- patch: Merged in feat/R9-for-shortdeck (pull request #316)
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog

### 0.9.9-stg
- docs: Update helm chart and changelog

### 0.9.9-dev
- patch: Replace datetime comparison with a broader range
- docs: Update helm chart and changelog

### 0.9.8
- docs: Update helm chart and changelog

### 0.9.8-stg
- docs: Update helm chart and changelog

### 0.9.8-dev
- patch: version bump
- fix: version bump
- fix: player state was not updated because of invalid future launch model
- docs: Update helm chart and changelog

### 0.9.7-dev
- patch: platform_id added to the response
- docs: Update helm chart and changelog

### 0.9.6-dev
- patch: WPTGO_URL
- docs: Update helm chart and changelog

### 0.9.5-dev
- patch: ARMS-268 check number of already added bots before adding more to tournament
- patch: clean up calculation of players to add to tournaments, add tests
- patch: dont try filling overlay without players to add
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.9.4
- docs: Update helm chart and changelog

### 0.9.4-dev
- patch: fix selection of players for tournaments
- docs: Update helm chart and changelog

### 0.9.3
- docs: Update helm chart and changelog

### 0.9.3-dev
- patch: fix automation not working due to broken players query
- docs: Update helm chart and changelog

### 0.9.2-dev
- patch: move botservice methods to static
- docs: Update helm chart and changelog

### 0.9.1
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.9.1-stg
- patch: fix GET tournament automation config

### 0.9.0
- docs: Update helm chart and changelog

### 0.9.0-stg
- docs: Update helm chart and changelog

### 0.9.0-dev
- minor: ARMS-268 limit number of bots in tournament by 25% if tournament guarantee <= 10k$, 35% if guarantee > 10k$
- also add a helper function for currency conversion
- docs: Update helm chart and changelog

### 0.8.6-dev
- patch: fake commit
- Merged in feat/r8-shortdeck-introduced (pull request #306)
- R8/Shortdeck introduced
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog

### 0.8.5-stg
- docs: Update helm chart and changelog

### 0.8.5-dev
- patch: fix creating automation config
- docs: Update helm chart and changelog

### 0.8.4-dev
- patch: new deploy
- Tournament versions replacing lock mechanism
- docs: Update helm chart and changelog

### 0.8.3-dev
- patch: move player filtering by play_time_range from in-memory to db query
- docs: Update helm chart and changelog

### 0.8.2-dev
- Merged in fix/return_patch_player (pull request #302)
- patch: return PATCH /players/{player_id}
- patch: return PATCH /players/{player_id}
- docs: Update helm chart and changelog
- docs: Update helm chart and changelog

### 0.8.1-dev
- patch: change player selection for overlay_filler from in-memory filtering to db query

### 0.7.4-dev
- Merged in feat/refactor_bots_to_use_type_enum (pull request #298)
- patch: refactor bot service and related code to use bot type as enum instead of string
- patch: refactor bot service and related code to use bot type as enum instead of string
- Merge branch 'main' into feat/refactor_bots_to_use_type_enum
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.7.3-dev
- Merged in fix/cleanup_bot_response_handling (pull request #297)
- Fix/cleanup bot response handling
- patch: unify bot start response handling. cleanup calculating of reserved players
- add missing imports
- Approved-by: nikolai.liubavin
- docs: Update helm chart and changelog

### 0.7.2-dev
- Merged in patch/fix_tables_automation_trigger (pull request #295)
- patch: fix tables automation conditions
- Approved-by: Alexandr Maximov
- enable feature flags
- Merge remote-tracking branch 'origin' into patch/fix_tables_automation_trigger
- rework querying tables for automation from in-memory filtering to db requests
- docs: Update helm chart and changelog
- patch: fix tables automation conditions
- before that tables automation did not work because filtering tables by BigBlinds was incorrect
- Also rename table automation model fields  properly

### 0.7.1-dev
- patch: fix tables automation conditions
- before that tables automation did not work because filtering tables by BigBlinds was incorrect
- Also rename table automation model fields  properly
- docs: Update helm chart and changelog

### 0.7.0-dev
- minor: add configurable currency field to filtering tables for autostart
- switch currency filtering options to a predefined enum
- fix user balance check for automation
- fix tables automation scheduler
- split tables automation task in separate `start` and `stop` methods
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.6.2-dev
- patch: add country field for users in database and in add/register user requests
- patch: rewrite players API to functional style
- remove duplicated request model for IP pool configuration
- remove redundant API url
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.6.1-dev
- patch: IP pool API now returns proper error text messages.
- Rewrite IP pool router module from class style to functional style recommended by FastAPI docs
- patch: simplify error handling for ip_pool endpoint
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.6.0-dev
- minor: add player registration on WPTGO platform
- Merged in fix/improve-stop-matching-players-with-more-logs (pull request #270)
- Improved stop_matching_players function with more logs and added delete configuration by id endpoint
- Improved stop_matching_players function with more logs and added delete configuration by id endpoint
- Returned HTTP 204 No Content for successful deletion in delete_tables_automation_config
- Merge branch 'main' into fix/improve-stop-matching-players-with-more-logs
- fix tests, cleanup code style
- indentaion fix
- Merge branch 'main' into fix/improve-stop-matching-players-with-more-logs
- clean up match criteria for stopping bots
- codestyle fix
- fix table automation tests
- Approved-by: Alexandr Maximov
- Merged in ARMS-234-table-automation-update-tracking (pull request #273)
- ARMS-234 Added started_count, stopped_count and updated_at fields and checked new implementations in tests
- ARMS-234 Added started_count, stopped_count and updated_at fields and checked new implementations in tests
- Merge branch 'main' into ARMS-234-table-automation-update-tracking
- Approved-by: Alexandr Maximov
- Merged in feat/logger-for-everything (pull request #198)
- Feat/logger for everything
- init
- router added
- Approved-by: Illia Komsa
- docs: Update helm chart and changelog

### 0.5.6
- docs: Update helm chart and changelog

### 0.5.6-stg
- docs: Update helm chart and changelog

### 0.5.6-dev
- patch: lock for tournaments
- docs: Update helm chart and changelog

### 0.5.5
- docs: Update helm chart and changelog

### 0.5.5-stg
- docs: Update helm chart and changelog

### 0.5.5-dev
- patch: Fix wrong version format in toml updated by pipeline
- patch: new deploy
- patch: revert config always turned on
- docs: Update helm chart and changelog

### 0.5.4-stg
- docs: Update helm chart and changelog

### 0.5.4-dev
- patch: Logs for tournaments overlay fill
- docs: Update helm chart and changelog

### 0.5.3
- docs: Update helm chart and changelog

### 0.5.3-stg
- docs: Update helm chart and changelog

### 0.5.3-dev
- patch: fill overlay logs
- TOP-311 Removing unused /metric endpoint from scarpe list
- docs: Update helm chart and changelog

### 0.5.2-dev
- patch: Platform delete method
- docs: Update helm chart and changelog

### 0.5.1
- docs: Update helm chart and changelog

### 0.5.1-stg
- docs: Update helm chart and changelog

### 0.5.1-dev
- patch: return of the scanning bot
- docs: Update helm chart and changelog

### 0.5.0-dev
- minor: transfer bot scheduling
- docs: Update helm chart and changelog

### 0.4.4-dev
- patch: R7 IP Pool deploy
- scheduler reverted
- cleanup
- enabling IP_Pool for R7 Dev
- docs: Update helm chart and changelog

### 0.4.3
- docs: Update helm chart and changelog

### 0.4.3-stg
- docs: Update helm chart and changelog

### 0.4.3-dev
- patch: use local data source for fflags if no sdk key
- docs: Update helm chart and changelog

### 0.4.2-dev
- patch: transfer api
- docs: Update helm chart and changelog

### 0.4.1-dev
- patch: account field in register method and docs removed deploy
- Merged in bugfix/register_wrong_username (pull request #279)
- Bugfix/register wrong username
- Fix wrong account on the worker side
- Enable ip pool by default
- Approved-by: Alexandr Maximov
- docs: Update helm chart and changelog

### 0.4.0-dev
- minor: add decorator for consistent JSON responses
- docs: Update helm chart and changelog

### 0.3.0-dev
- minor: platforms support
- Merged in feature/updating-boss-api (pull request #277)
- Updating Boss api url
- Approved-by: Aleksei Klimakov
- Approved-by: Yongtao Ma
- docs: Update helm chart and changelog

### 0.2.18-dev
- patch: Merged in feature/max_players-per-ip_improved (pull request #274)
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.2.17
- docs: Update helm chart and changelog

### 0.2.17-stg
- docs: Update helm chart and changelog

### 0.2.17-dev
- Merged in feature/max_players-per-ip_improved (pull request #272)
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.2.16
- docs: Update helm chart and changelog

### 0.2.16-stg
- docs: Update helm chart and changelog

### 0.2.16-dev
- patch: Merged in feature/max_players-per-ip_improved (pull request #271)
- IP POOL: Max_players_per_IP logic improved
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.2.15
- docs: Update helm chart and changelog

### 0.2.15-stg
- docs: Update helm chart and changelog

### 0.2.15-dev
- patch: max_players_per_ip log
- Merged in feature/max-players-per-ip-setting (pull request #269)
- MAX_PLAYERS_PER_IP setting introduced
- Approved-by: Aleksei Klimakov
- docs: remove reference to launchdarkly-sdk-key
- TOP-276 Setting requests/limits
- TOP-276 docs: Resizing req/limits
- TOP-276 Setting requests/limits
- docs: set launchdarkly-sdk-key as optional
- docs: Update helm chart and changelog

### 0.2.14
- docs: Update helm chart and changelog

### 0.2.14-stg
- docs: Update helm chart and changelog

### 0.2.13-dev
- patch: adjust day one mtt game pool

### 0.2.12-dev
- patch: fixed players_count issue
- docs: Update helm chart and changelog

### 0.2.11-stg
- docs: Update helm chart and changelog

### 0.2.11-dev
- patch: Added more detailed logs for the table filtering process flow
- ARMS-213 Implemented Automation 2nd iteration
- docs: Update helm chart and changelog

### 0.2.10-dev
- patch: r7_ip_pool_enabled usage added
- docs: Update helm chart and changelog

### 0.2.9-dev
- patch: fake commit for deploy
- R7 IP Pool is temporarily disabled
- docs: Update helm chart and changelog

### 0.2.8
- docs: Update helm chart and changelog

### 0.2.8-stg
- docs: Update helm chart and changelog

### 0.2.8-dev
- patch: Fixed logs new line issue for Grafana
- docs: Update helm chart and changelog

### 0.2.7-dev
- patch: Add logging for automation.
- docs: Update helm chart and changelog

### 0.2.6-dev
- patch: Merged in fix/ip-pool-assignment-fix (pull request #261)
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.2.5
- docs: Update helm chart and changelog

### 0.2.5-stg
- docs: Update helm chart and changelog

### 0.2.5-dev
- patch: now max player per ippool is 1
- docs: Update helm chart and changelog

### 0.2.4-stg
- docs: Update helm chart and changelog

### 0.2.4-dev
- TOP-250 patch: Switching to the different nodepool
- TOP-250 patch: Switching to the different nodepool
- docs: Update helm chart and changelog

### 0.2.3-dev
- patch: fake commit
- ip_pool maximum players per ip reduced to 3
- docs: Update helm chart and changelog

### 0.2.2-dev
- patch: Withdraw support
- docs: Update helm chart and changelog

### 0.2.1
- docs: Update helm chart and changelog

### 0.2.1-stg
- docs: Update helm chart and changelog

### 0.2.1-dev
- patch: helm chart value urgent update
- docs: Update helm chart and changelog

### 0.2.0-stg
- docs: Update helm chart and changelog

### 0.2.0-dev
- minor: Merged in fix/scan_job_stall_timeout_refactored (pull request #249)
- Fix/scan job stall timeout refactored
- Approved-by: Aleksei Klimakov
- docs: Update helm chart and changelog

### 0.1.22
- docs: Update helm chart and changelog

### 0.1.22-stg
- docs: Update helm chart and changelog

### 0.1.22-dev
- patch: Improvements for automation

### 0.1.21
- docs: Update helm chart and changelog

### 0.1.21-stg
- docs: Fix SDK secret key syntax
- Fix SDK secret key syntax

### 0.1.21-dev
- patch: TOP-211 Add launchdarkly_sdk_key as secret
- TOP-211 Fix sdk key secret naming
- TOP-211 Add LAUNCHDARKLY_SDK_KEY as ESO secret

### 0.1.20-dev
- patch: ARMS-212 Modified tables automation config datetime, rebuy and buyin fields
- docs: Update pipelines to support test developments
- docs: Update pipelines to support test developments
- docs: Update helm chart and changelog

### 0.1.19-dev
- patch: Remove repeated config urls
- docs: Update helm chart and changelog

### 0.1.18-dev
- patch: IP Pool is  explicitly enabled for R5
- docs: increasing prod tag manually per request

### 0.1.17-dev
- patch: R1 R3  set to True for Stg, Prod
- docs: Update helm chart and changelog

### 0.1.16-dev
- patch: R1_IP_POOL_ENABLED set to True for Dev
- docs: Update helm chart and changelog

### 0.1.15
- docs: Update helm chart and changelog

### 0.1.15-stg
- docs: Update helm chart and changelog

### 0.1.15-dev
- patch: Fix register method name
- Fix register method name
- docs: Update helm chart and changelog

### 0.1.14-dev
- patch: R3_IP_POOL_ENABLED set to True for Dev
- docs: Update helm chart and changelog

### 0.1.13
- docs: Update helm chart and changelog

### 0.1.13-stg
- docs: Update helm chart and changelog

### 0.1.13-dev
- patch: prod salt added
- Salt fixture for prod
- docs: Update helm chart and changelog

### 0.1.12
- docs: Update helm chart and changelog

### 0.1.12-stg
- docs: Update helm chart and changelog

### 0.1.12-dev
- patch: config urls for backward compatibility
- docs: Update helm chart and changelog

### 0.1.11-dev
- patch: Add wpk response log to player register
- docs: Update helm chart and changelog

### 0.1.10-stg
- docs: Update helm chart and changelog

### 0.1.10-dev
- patch: Add wpk response log to player register
- boss api via nginx
- docs: Update helm chart and changelog

### 0.1.9-stg
- docs: Update helm chart and changelog

### 0.1.9-dev
- patch: Add scheduler for R1&R2 automation
- docs: Update helm chart and changelog

### 0.1.8-dev
- Merge branch 'main' of https://bitbucket.org/aceguardian/manager
- patch: Lint fix
- docs: Update helm chart and changelog

### 0.1.7-dev
- patch: WPK_URL added to dev yaml, Gitignore fixed
- docs: updating pod resources

### 0.1.5-stg
- docs: Update helm chart and changelog

### 0.1.5-dev
- patch: lint fixes
- Updating WPK_URL for different envs
- TOP-189 docs: Fixing configmap
- docs: Update helm chart and changelog

### 0.1.4-dev
- patch: fake commit for dev
- docs: Update helm chart and changelog

### 0.1.3
- docs: Update helm chart and changelog

### 0.1.3-stg
- docs: Pipeline scripts update
- docs: Update helm chart and changelog

### 0.1.3-dev
- patch: error fixed
- docs: Update helm chart and changelog

### 0.1.2-dev
- patch: ip-pool envs described in values.yaml file
- docs: Update helm chart and changelog

### 0.1.1-dev
- patch: create new migration for TablesAutomationConfig
- docs: Update helm chart and changelog

### 0.1.0-dev
- TOP-183 patch: testing botmanager cicd
