from typing import List
from unittest.mock import AsyncMock, patch

import pytest
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from pydantic import ValidationError

from src.db.models import Player
from src.schemas.requests import (
    AddUserAccountRequest,
    AddMultipleUserAccountsRequest,
    RegisterPlayerRequest,
)
from src.schemas.responses import AddUserAccountResponse
from src.services.player_registration_service import PlayerRegistrationService
from src.services.players import PlayerService
from src.utils.enums import PlatformType


@pytest.mark.asyncio
@patch("src.services.ip_pool_service.IPPoolConfigurationService.assign_player_to_ip")
@patch("src.services.ip_pool_service.IPPoolConfigurationService.get_free_ip")
@patch("src.services.platforms.wpk.WPKService.register_user")
@patch("src.services.player_registration_service.post")
async def test_register_players_success(
    post_mock, register_user_mock, get_ip_mock, assign_ip_mock, mock_db
):
    service = PlayerRegistrationService()

    get_ip_mock.return_value = AsyncMock(external_ip="*******", ip_conf_id="conf123")
    register_user_mock.return_value = {
        "data": {"userId": "uid123", "nickname": "testuser", "account": "acc123"}
    }
    post_mock.return_value = None  # `post` is used without return

    req = RegisterPlayerRequest(
        platform_id=PlatformType.WPK.value,
        app_id=1,
        account="acc123",
        username="user123",
        country_code="US",
        phone_number="**********",
    )

    result = await service.register_players([req])

    assert len(result) == 1
    assert result[0].user_id == "uid123"
    assert result[0].platform_id == 98


@pytest.mark.asyncio
@patch("src.services.ip_pool_service.IPPoolConfigurationService.assign_player_to_ip")
@patch("src.services.ip_pool_service.IPPoolConfigurationService.get_free_ip")
@patch("src.services.player_registration_service.post")
async def test_add_user_accounts_success(post_mock, get_ip_mock, assign_ip_mock, mock_db):
    get_ip_mock.return_value = AsyncMock(external_ip="*******", ip_conf_id="conf123")
    post_mock.return_value = None  # `post` is used without return

    req = AddMultipleUserAccountsRequest(
        app_id=1,
        platform_id=PlatformType.WPK.value,
        accounts=[
            AddUserAccountRequest(
                username="user123",
                account="acc123",
                country_code="US",
                password="password123",
                phone_number="**********",
            ),
        ],
    )

    result: List[AddUserAccountResponse] = await PlayerService.add_user_accounts(req)

    assert len(result) == 1
    assert result[0].external_ip == "*******"
    assert result[0].platform_id == 98


@pytest.mark.asyncio
async def test_requires_country():
    with pytest.raises(ValidationError):
        req = AddMultipleUserAccountsRequest(
            app_id=1,
            platform_id=1,
            accounts=[
                AddUserAccountRequest(
                    username="user123",
                    account="acc123",
                    password="password123",
                    phone_number="**********",
                ),
            ],
        )
        await PlayerService.add_user_accounts(req)


@pytest.mark.asyncio
@patch("src.services.ip_pool_service.IPPoolConfigurationService.assign_player_to_ip")
@patch("src.services.ip_pool_service.IPPoolConfigurationService.get_free_ip")
@patch("aiohttp.ClientSession.post")
@patch("src.utils.logging.Logger.info")
@patch("src.services.player_registration_service.post")
async def test_wptgo_with_optional_fields_register_players_success(
    post_mock, logger_mock, session_post_mock, get_ip_mock, assign_ip_mock, mock_db
):
    service = PlayerRegistrationService()

    get_ip_mock.return_value = AsyncMock(external_ip="*******", ip_conf_id="conf123")
    post_mock.return_value = None  # `post` is used without return

    req = RegisterPlayerRequest(
        platform_id=PlatformType.WPTGO.value,
        app_id=1,
        account="acc123",
        username="user123",
        country_code="US",
        phone_number="**********",
        optional_field_1=True,
        optional_field_2="value2",
    )

    result = await service.register_players([req])
    found = False
    for call in logger_mock.call_args_list:
        print(call.args[1])
        if "Adding optional fields to payload" in call.args[1]:
            found = True
            assert "optional_field_1" in call.args[1]
            break
    assert found
    assert len(result) == 1


# Tests for change_avatar function
@pytest.mark.asyncio
@patch("src.services.worker_service.WorkerService.get_user_id_from_worker")
@patch("src.services.platforms.wptgo.WptgoService.change_avatar")
async def test_change_avatar_success(wptgo_change_avatar_mock, worker_get_user_id_mock, mock_db):
    """Test successful avatar change for WPTGO player."""
    service = PlayerRegistrationService()

    player = Player(
        player_id="test_player_id",
        app_id=1,
        platform_id=PlatformType.WPTGO.value,
        enabled=True,
        status="idle"
    )
    await player.save()

    worker_get_user_id_mock.return_value = "test_user_id"
    wptgo_change_avatar_mock.return_value = {
        "avatarType": 2,
        "avatarUrl": "http://34.126.94.45:2631/avatar_41792_1754578510.png?avatarType=Custom"
    }

    await service.change_avatar(
        player_id="test_player_id",
        avatarBase64="base64_image_data",
        imgExt="jpg"
    )

    worker_get_user_id_mock.assert_called_once_with(player_id="test_player_id")
    wptgo_change_avatar_mock.assert_called_once_with(
        avatarBase64="base64_image_data",
        imgExt="jpg",
        user_id="test_user_id"
    )

    player = await Player.find_one(Player.player_id == "test_player_id")
    assert player.avatar_changed


@pytest.mark.asyncio
async def test_change_avatar_player_not_found(mock_db):
    """Test avatar change when player doesn't exist."""
    service = PlayerRegistrationService()

    with pytest.raises(HTTPException) as exc_info:
        await service.change_avatar(
            player_id="nonexistent_player",
            avatarBase64="base64_image_data",
            imgExt="jpg"
        )

    assert exc_info.value.status_code == 404
    assert "Player nonexistent_player not found" in str(exc_info.value.detail)


@pytest.mark.asyncio
async def test_change_avatar_not_wptgo_player(mock_db):
    """Test avatar change when player is not a WPTGO player."""
    service = PlayerRegistrationService()

    player = Player(
        player_id="wpk_player_id",
        app_id=1,
        platform_id=PlatformType.WPK.value,
        enabled=True,
        status="idle"
    )
    await player.save()

    with pytest.raises(HTTPException) as exc_info:
        await service.change_avatar(
            player_id="wpk_player_id",
            avatarBase64="base64_image_data",
            imgExt="jpg"
        )

    assert exc_info.value.status_code == 400
    assert "avatar change is not supported for platforms other than WPTGO" in str(exc_info.value.detail)


@pytest.mark.asyncio
@patch("src.services.worker_service.WorkerService.get_user_id_from_worker")
async def test_change_avatar_user_id_not_found(worker_get_user_id_mock, mock_db):
    """Test avatar change when worker service doesn't return user_id."""
    service = PlayerRegistrationService()

    player = Player(
        player_id="test_player_id",
        app_id=1,
        platform_id=PlatformType.WPTGO.value,
        enabled=True,
        status="idle"
    )
    await player.save()

    worker_get_user_id_mock.return_value = None

    with pytest.raises(HTTPException) as exc_info:
        await service.change_avatar(
            player_id="test_player_id",
            avatarBase64="base64_image_data",
            imgExt="jpg"
        )

    assert exc_info.value.status_code == 404
    assert "User_id for player test_player_id not found" in str(exc_info.value.detail)
