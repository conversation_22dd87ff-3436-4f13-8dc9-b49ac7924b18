from datetime import datetime, timedelta, time, timezone

import pytest
import pytest_asyncio
from unittest.mock import patch
from beanie.odm.operators.update.general import Set

from src.db.models import (
    GamingConfiguration,
    IPPoolConfiguration,
    PlayerBalance,
    Table,
    TablesAutomationConfig,
    Player,
    PlayTimeRange,
    Launch,
)
from src.schemas.requests import TablesAutomationConfigRequest
from src.services.automation import AutomationService
from src.tasks.tables_automation import (
    get_matching_tables,
    find_free_player_for_table,
    find_players_to_stop, start_scheduled_tables,
    filter_players_using_same_ip,
    get_strategy_profile,
    try_start_bot
)
from src.utils.enums import BotType, PlayerStatus, CurrencyType


now = datetime.now(timezone.utc)
hour_before = now - timedelta(hours=1)


@pytest_asyncio.fixture
async def mock_tables_automation_config():
    tables_automation_config_data = [
        TablesAutomationConfig(
            config_id=1,
            app_id=82,
            is_enabled=True,
            start_interval=5,
            start_last_update=now,
            stop_interval=3,
            stop_last_update=hour_before,
            created_at=now,
            big_blinds=[100, 200, 320],
            room_modes=[0, 2],
            ante=100,
            players_count=[4, 7],
            currencies=[CurrencyType.DIAMOND],
            buy_in_multipliers=[100, 200, 300],
        ),
        TablesAutomationConfig(
            config_id=2,
            app_id=82,
            is_enabled=False,
            start_interval=5,
            start_last_update=now,
            stop_interval=3,
            stop_last_update=hour_before,
            created_at=now,
            big_blinds=[140, 200, 350],
            room_modes=[0, 2],
            ante=300,
            players_count=[4, 7],
            currencies=[CurrencyType.DIAMOND],
            buy_in_multipliers=[100, 200, 300],
        ),

        TablesAutomationConfig(
            config_id=3,
            app_id=83,
            is_enabled=True,
            start_interval=5,
            start_last_update=now,
            stop_interval=3,
            stop_last_update=hour_before,
            created_at=now,
            big_blinds=[100, 280, 450],
            room_modes=[0, 2],
            ante=150,
            currencies=[CurrencyType.USD],
            buy_in_multipliers=[100, 200, 300]
        ),

        TablesAutomationConfig(
            config_id=4,
            app_id=83,
            is_enabled=True,
            start_interval=5,
            start_last_update=now,
            stop_interval=3,
            stop_last_update=hour_before,
            created_at=now,
            big_blinds=[400, 900, 1200],
            room_modes=[0, 2],
            ante=200,
            currencies=[CurrencyType.USD],
            buy_in_multipliers=[100, 200, 300]
        ),
        ]
    await TablesAutomationConfig.insert_many(tables_automation_config_data)


@pytest_asyncio.fixture
async def mock_players(mock_db):
    def get_player(**kwargs):
        return Player(
            app_id=81,
            status=PlayerStatus.PLAYING.value,
            enabled=True,
            country_code="US",
            **kwargs
        )

    await Player.insert_many([
        get_player(
            player_id="player_1",
            balance=PlayerBalance(gold=(3*8)),
            stack=5000,
            hands_played=115,
            rebuy_count=1,
            play_time_range=PlayTimeRange(start=time.min, end=time.max),
        ),
        get_player(
            player_id="player_3",
            balance=PlayerBalance(gold=(2000)),
            stack=35000,
            hands_played=50,
            rebuy_count=2
        ),
        get_player(
            player_id="player_4",
            balance=PlayerBalance(diamond=122000),
            stack=0,
            hands_played=36,
            rebuy_count=1
        ),
    ])


@pytest_asyncio.fixture
async def mock_tables():
    """Fixture to insert mock Table data."""
    table_data = [
        Table(
            table_id="t1",
            table_name="Table 1",
            app_id=81,
            gaming_configuration=GamingConfiguration(
                game="Texas Hold'em",
                blinds=[2, 4, 8],
                ante=12,
                game_mode=1,
                room_mode=0,
                straddle=True
            ),
            currency="USD",
            players_total=6,
            empty_seats=3,
        ),
        Table(
            table_id="t2",
            table_name="Table 2",
            app_id=81,
            gaming_configuration=GamingConfiguration(
                game="Omaha",
                blinds=[3, 6, 12],
                ante=12,
                game_mode=2,
                room_mode=2,
                straddle=True
            ),
            currency="USD",
            players_total=5,
            empty_seats=2
        ),
        Table(
            table_id="t3",
            table_name="Table 3",
            app_id=82,
            gaming_configuration=GamingConfiguration(
                game="Omaha",
                blinds=[100, 200],
                ante=100,
                game_mode=2,
                room_mode=2,
                straddle=False
            ),
            currency="DIAMOND",
            players_total=6,
            empty_seats=2
        ),
    ]

    await Table.insert_many(table_data)


@pytest.mark.asyncio
async def test_tables_automation_config(mock_db, mock_tables_automation_config):
    configs = await TablesAutomationConfig.find().to_list()
    assert len(configs) == 4

    r1_config = await TablesAutomationConfig.find({"app_id": 82}).to_list()
    assert len(r1_config) == 2

    r1_config_app_id = await TablesAutomationConfig.find({"app_id": 82, "config_id": 2}).to_list()
    assert len(r1_config_app_id) == 1

    r2_config = await TablesAutomationConfig.find({"app_id": 83}).to_list()
    assert len(r2_config) == 2

    r2_config_app_id = await TablesAutomationConfig.find({"app_id": 83, "config_id": 4}).to_list()
    assert len(r2_config_app_id) == 1


@pytest.mark.asyncio
async def test_get_tables_automation_config(mock_db, mock_tables_automation_config):
    config = await AutomationService.get_tables_automation_config(82, config_id=1)
    assert config[0].big_blinds == [100, 200, 320]

    config = await AutomationService.get_tables_automation_config(82, config_id=2)
    assert config[0].big_blinds == [140, 200, 350]

    config = await AutomationService.get_tables_automation_config(83, config_id=3)
    assert config[0].big_blinds == [100, 280, 450]

    config = await AutomationService.get_tables_automation_config(83, config_id=4)
    assert config[0].big_blinds == [400, 900, 1200]

    config = await AutomationService.get_tables_automation_config(82)
    assert len(config) == 2

    config = await AutomationService.get_tables_automation_config(83)
    assert len(config) == 2


@pytest.mark.asyncio
async def test_set_tables_automation_config_id_not_exist(mock_db, mock_tables_automation_config):
    request = TablesAutomationConfigRequest(
        app_id=82, is_enabled=False, start_interval=10,
        stop_interval=5, big_blinds=[500, 1000], room_modes=[0, 2], ante=200,
        currencies=[CurrencyType.DIAMOND],
        occupancy_threshold=999,
    )

    config = await AutomationService.set_tables_automation_config(request)
    assert config.big_blinds == [500, 1000]
    assert config.config_id == 5


@pytest.mark.asyncio
async def test_set_tables_automation_config_exist(mock_db, mock_tables_automation_config):
    config = TablesAutomationConfig(
        app_id=82, config_id=2, is_enabled=True, start_interval=10, start_last_update=now,
        stop_interval=5, stop_last_update=hour_before, created_at=now, big_blinds=[500, 1000], room_modes=[0, 2], ante=180,
        currencies=[CurrencyType.DIAMOND])

    config = await AutomationService.set_tables_automation_config(config)
    assert config.big_blinds == [500, 1000]
    assert config.is_enabled


@pytest.mark.asyncio
async def test_set_tables_automation_invalid_config_id_for_app(mock_db, mock_tables_automation_config):
    config = TablesAutomationConfig(
        app_id=82, config_id=3, is_enabled=False, start_interval=10, start_last_update=now,
        stop_interval=5, stop_last_update=hour_before, created_at=now, big_blinds=[500, 1000], room_modes=[0, 2], ante=230,
        currencies=[CurrencyType.DIAMOND])

    with pytest.raises(ValueError):
        await AutomationService.set_tables_automation_config(config)


@pytest.mark.asyncio
async def test_set_tables_automation_config_id_exist_empty_table(mock_db):
    config = TablesAutomationConfig(
        app_id=82, config_id=2, is_enabled=True, start_interval=10, start_last_update=now,
        stop_interval=5, stop_last_update=hour_before, created_at=now, big_blinds=[500, 1000], room_modes=[0, 2], ante=310,
        currencies=[CurrencyType.DIAMOND])

    with pytest.raises(ValueError):
        await AutomationService.set_tables_automation_config(config)


@pytest.mark.asyncio
async def test_set_tables_automation_config_empty_table_without_config_id(mock_db):
    request = TablesAutomationConfigRequest(
        app_id=82, is_enabled=False, start_interval=10, start_last_update=now,
        stop_interval=5, stop_last_update=hour_before, created_at=now, big_blinds=[500, 1000], room_modes=[0, 2], ante=120,
        currencies=[CurrencyType.DIAMOND],
        occupancy_threshold=999,
    )
    config = await AutomationService.set_tables_automation_config(request)
    assert config.big_blinds == [500, 1000]
    assert config.config_id == 1


@pytest.mark.asyncio
async def test_get_configuration_matching_tables(mock_db, mock_tables, mock_tables_automation_config):
    """Test fetching matching tables based on config object"""
    config2 = await TablesAutomationConfig.find_one({"app_id": 82})
    tables_r2 = await get_matching_tables(config2)
    assert len(tables_r2) == 1


@pytest.mark.asyncio
async def test_find_free_player_for_table(mock_db):
    idle = PlayerStatus.IDLE.value
    now = datetime.now(timezone.utc).time()

    def get_player(player_id, **kwargs):
        return Player(app_id=13, status=idle, enabled=True, player_id=player_id, **kwargs)

    gc = GamingConfiguration(game='', blinds=[1, 3], straddle=False)
    usd_table = await Table(
        table_id="usd", table_name="", app_id=13, gaming_configuration=gc, currency=CurrencyType.USD, empty_seats=5
    ).save()
    gc2 = GamingConfiguration(game='', blinds=[1000, 2000], straddle=False)
    gold_table = await Table(
        table_id="gold", table_name="", app_id=13, gaming_configuration=gc2, currency=CurrencyType.GOLD, empty_seats=5
    ).save()
    gc3 = GamingConfiguration(game='', blinds=[200, 400], straddle=False)
    diamond_table = await Table(
        table_id="diamond", table_name="", app_id=13, gaming_configuration=gc3, currency=CurrencyType.DIAMOND, empty_seats=5
    ).save()

    await Player.insert_many([
        get_player("p1", balance=PlayerBalance(gold=3*8+1)),
        get_player("p2", balance=PlayerBalance(gold=100)),
        get_player("p3", balance=PlayerBalance(gold=2001)),
        get_player("p4", balance=PlayerBalance(diamond=300)),
        get_player("p5", balance=PlayerBalance(diamond=400)),
    ])
    player_usdt = await find_free_player_for_table(usd_table, now, 100)
    assert player_usdt.player_id in ("p1", "p2", "p3")

    player_gold = await find_free_player_for_table(gold_table, now, 100)
    assert player_gold.player_id == "p3"

    player_diamond = await find_free_player_for_table(diamond_table, now, 100)
    assert player_diamond.player_id == "p5"


@pytest.mark.asyncio
async def test_find_free_player_for_table_usd_balance(mock_db):
    """Test find_free_player_for_table when player has balance in USD and table is in USD."""
    await Player(
        player_id="player_usd",
        app_id=81,
        status=PlayerStatus.IDLE.value,
        enabled=True,
        country_code="US",
        balance=PlayerBalance(usd=10000),
        stack=5000,
        hands_played=10,
        rebuy_count=0,
        play_time_range=PlayTimeRange(start=time.min, end=time.max),
    ).save()

    gc = GamingConfiguration(game='', blinds=[1, 3], straddle=False)
    usd_table = await Table(
        table_id="usd", table_name="", app_id=81, gaming_configuration=gc, currency=CurrencyType.USD, empty_seats=5
    ).save()

    player = await find_free_player_for_table(usd_table, datetime.now(timezone.utc).time(), 200)
    assert player.player_id == "player_usd"


@pytest.mark.asyncio
async def test_find_players_to_stop(mock_db):
    app_id = 81

    def get_player(player_id, **kwargs):
        return Player(app_id=app_id, status=PlayerStatus.PLAYING.value, bot_id='b1', bot_type=BotType.PLAY.value, enabled=True, player_id=player_id,  **kwargs)

    await Player.insert_many([
        get_player("p1", table_id='t1', hands_played=100500, launch_id="la-1"),
        get_player("p2", table_id='t2', hands_played=50, launch_id="la-2"),
        get_player("p3", table_id='t3', hands_played=50, launch_id="la-3")
    ])

    await Launch.insert_many([
        Launch(launch_id="la-1", player_id="p1", table_id="t1", config_id=1, app_id=app_id),
        Launch(launch_id="la-2", player_id="p2", table_id="t2", config_id=1, app_id=app_id),
        Launch(launch_id="la-3", player_id="p3", table_id="t3", config_id=1, app_id=app_id),
    ])

    gc = GamingConfiguration(game="Test", blinds=[50, 100], straddle=False)
    await Table.insert_many([
        Table(table_id="t1", table_name='t1', app_id=app_id, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t2", table_name='t2', app_id=app_id, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t3", table_name='t3', app_id=app_id, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
    ])

    config = TablesAutomationConfig(
        app_id=app_id, config_id=1, is_enabled=True, start_interval=1, stop_interval=1,
        big_blinds=[], room_modes=[], ante=0, players_count=[], currencies=[]
    )

    matching_players = await find_players_to_stop(config)

    matching_player_ids = {player.player_id for player in matching_players}

    assert matching_player_ids == {"p1"}


@pytest.mark.asyncio
async def test_find_players_to_stop_max_hands_to_play(mock_db):
    def get_player(player_id, **kwargs):
        return Player(app_id=0, bot_id='bot', status=PlayerStatus.PLAYING.value, bot_type=BotType.PLAY.value, enabled=True, player_id=player_id,  **kwargs)

    await Player.insert_many([
        get_player("p1", hands_played=420, table_id='t1', launch_id="la-1"),
        get_player("p2", hands_played=50, table_id='t1', launch_id="la-2"),
        get_player("p3", hands_played=50, table_id='t1', launch_id="la-3")
    ])

    await Launch.insert_many([
        Launch(launch_id="la-1", player_id="p1", table_id="t1", app_id=0, config_id=1, soft_max_hands_played=100500),
        Launch(launch_id="la-2", player_id="p2", table_id="t1", app_id=0, config_id=1, soft_max_hands_played=49),
        Launch(launch_id="la-3", player_id="p3", table_id="t1", app_id=0, config_id=1, soft_max_hands_played=51),
    ])

    gc = GamingConfiguration(game="Test", blinds=[50, 100], straddle=False)
    await Table.insert_many([
        Table(table_id="t1", table_name='t1', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
    ])

    config = TablesAutomationConfig(
        app_id=0, config_id=1, is_enabled=True, start_interval=1, stop_interval=1,
        big_blinds=[], room_modes=[], ante=0, players_count=[], currencies=[]
    )

    matching_players = await find_players_to_stop(config)
    matching_player_ids = {player.player_id for player in matching_players}

    assert matching_player_ids == {"p2"}


@pytest.mark.asyncio
async def test_find_players_to_stop_with_max_rebuy_count(mock_db):
    playing = PlayerStatus.PLAYING.value
    bot_type = BotType.PLAY.value
    await Player.insert_many([
        Player(
            player_id="p1", table_id='t1', app_id=0, status=playing, bot_id='bot', bot_type=bot_type,
            enabled=True, launch_id="la-1", hands_played=10, rebuy_count=0),
        Player(
            player_id="p2", table_id='t2', app_id=0, status=playing, bot_id='bot', bot_type=bot_type,
            enabled=True, launch_id="la-2", hands_played=200, rebuy_count=1),
        Player(
            player_id="p3", table_id='t3', app_id=0, status=playing, bot_id='bot', bot_type=bot_type,
            enabled=True, launch_id="la-3", hands_played=10, rebuy_count=2),
        Player(
            player_id="p4", table_id='t4', app_id=0, status=playing, bot_id='play:bot', bot_type=None,
            enabled=True, launch_id="la-4", hands_played=10, rebuy_count=3),
    ])

    await Launch.insert_many([
        Launch(launch_id="la-11", player_id="p1", table_id="t1", app_id=0, config_id=1),
        Launch(launch_id="la-22", player_id="p2", table_id="t2", app_id=0, config_id=1),
        Launch(launch_id="la-33", player_id="p3", table_id="t3", app_id=0, config_id=1),
        Launch(launch_id="la-43", player_id="p4", table_id="t4", app_id=0, config_id=1),
    ])

    config = TablesAutomationConfig(
        app_id=0, config_id=1, is_enabled=True, start_interval=1, stop_interval=1,
        big_blinds=[], room_modes=[], ante=0, players_count=[], currencies=[]
    )
    gc = GamingConfiguration(game="Test", blinds=[50, 100], straddle=False)
    await Table.insert_many([
        Table(table_id="t1", table_name='t1', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t2", table_name='t2', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t3", table_name='t3', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t4", table_name='t4', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
    ])

    matching_players = await find_players_to_stop(config)
    matching_player_ids = set(player.player_id for player in matching_players)

    assert matching_player_ids == {"p2"}

    config.max_rebuy_count = 2
    matching_players = await find_players_to_stop(config)
    matching_player_ids = set(player.player_id for player in matching_players)

    assert matching_player_ids == {"p2", "p4"}


@pytest.mark.asyncio
async def test_find_players_to_stop_even_without_bot_id(mock_db):
    playing = PlayerStatus.PLAYING.value
    await Player.insert_many([
        Player(player_id="p1", app_id=0, status=playing, bot_id="play:99:1234567890", bot_type=BotType.PLAY.value,
               enabled=True, launch_id="la-1", hands_played=100500, rebuy_count=0),
    ])

    await Launch.insert_many([
        Launch(launch_id="la-1", player_id="p1", table_id="t1", app_id=0, config_id=1),
    ])

    config = TablesAutomationConfig(
        app_id=0, config_id=1, is_enabled=True, start_interval=1, stop_interval=1,
        big_blinds=[], room_modes=[], ante=0, players_count=[], currencies=[]
    )

    matching_players = await find_players_to_stop(config)
    matching_player_ids = set(player.player_id for player in matching_players)

    assert matching_player_ids == {"p1"}


@pytest.mark.asyncio
async def test_find_players_to_stop_players_left(mock_db):
    playing = PlayerStatus.PLAYING.value
    await Player.insert_many([
        Player(player_id="p1", table_id='t1', app_id=0, status=playing, bot_id='bot', bot_type=BotType.PLAY.value,
               enabled=True, launch_id="la-1", hands_played=10, rebuy_count=0),
        Player(player_id="p2", table_id='t2', app_id=0, status=playing, bot_id='bot', bot_type=BotType.PLAY.value,
               enabled=True, launch_id="la-2", hands_played=10, rebuy_count=0),
        Player(player_id="p3", table_id='t3', app_id=0, status=playing, bot_id='bot', bot_type=BotType.PLAY.value,
               enabled=True, launch_id="la-3", hands_played=10, rebuy_count=0),
        Player(player_id="p4", table_id='t4', app_id=0, status=playing, bot_id='bot', bot_type=BotType.PLAY.value,
               enabled=True, launch_id="la-4", hands_played=10, rebuy_count=0),
    ])

    await Launch.insert_many([
        Launch(launch_id="la-11", player_id="p1", table_id="t1", app_id=0, config_id=1),
        Launch(launch_id="la-22", player_id="p2", table_id="t2", app_id=0, config_id=1),
        Launch(launch_id="la-33", player_id="p3", table_id="t3", app_id=0, config_id=1),
        Launch(launch_id="la-43", player_id="p4", table_id="t4", app_id=0, config_id=1),
    ])
    gc = GamingConfiguration(game="Test", blinds=[50, 100], straddle=False)
    await Table.insert_many([
        Table(table_id="t1", table_name='t1', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t2", table_name='t2', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=3, empty_seats=0),
        Table(table_id="t3", table_name='t3', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=5, empty_seats=0),
        Table(table_id="t4", table_name='t4', app_id=0, gaming_configuration=gc, currency="DIAMOND", players_total=6, empty_seats=0),
    ])

    config = TablesAutomationConfig(
        app_id=0, config_id=1, is_enabled=True, start_interval=1, stop_interval=1,
        big_blinds=[], room_modes=[], ante=0, players_count=[], currencies=[],
        min_players_count_to_stop=2, max_players_count_to_stop=6
    )

    matching_players = await find_players_to_stop(config)

    matching_player_ids = {player.player_id for player in matching_players}

    assert matching_player_ids == {"p1", "p4"}

    config.min_players_count_to_stop = 0
    config.max_players_count_to_stop = 8

    matching_players = await find_players_to_stop(config)

    matching_player_ids = {player.player_id for player in matching_players}

    assert not matching_player_ids


@pytest.mark.asyncio
async def test_delete_tables_automation_config(mock_db, mock_tables_automation_config):

    config_before_delete = await TablesAutomationConfig.find({"config_id": 1}).to_list()
    assert len(config_before_delete) == 1
    await AutomationService.delete_tables_automation_config(82, 1)

    config_after_delete = await TablesAutomationConfig.find({"config_id": 1}).to_list()
    assert len(config_after_delete) == 0


@pytest.mark.asyncio
async def test_delete_tables_automation_config_not_found(mock_db, mock_tables_automation_config):

    with pytest.raises(ValueError):
        await AutomationService.delete_tables_automation_config(82, 99)


@pytest.mark.asyncio
@patch('src.tasks.tables_automation.get_matching_tables')
@patch('src.tasks.tables_automation.find_free_player_for_table')
@patch('src.tasks.tables_automation.BotService.start_bot')
async def test_start_scheduled_tables_respects_max_bots_per_table(
    start_bot_mock, find_free_player_mock, get_tables_mock, mock_db
):
    config = TablesAutomationConfig(
        config_id=1,
        app_id=81,
        is_enabled=True,
        max_bots_per_table=2,
        table_rebuy_threshold=0,
        big_blinds=[100, 200],
        room_modes=[0],
        ante=0,
        players_count=[4],
        currencies=[CurrencyType.DIAMOND],
        start_interval=1,
        stop_interval=1,
    )

    table = Table(
        table_id="t1",
        table_name="Table 1",
        app_id=81,
        gaming_configuration=GamingConfiguration(
            game="Test",
            blinds=[50, 100, 200],
            ante=0,
            game_mode=1,
            room_mode=0,
            straddle=False
        ),
        currency="DIAMOND",
        players_total=6,
        empty_seats=3,
    )
    get_tables_mock.return_value = [table]

    # Insert 2 players (max reached)
    await Player.insert_many([
        Player(player_id="p1", app_id=81, table_id="t1", status=PlayerStatus.PLAYING.value, enabled=True, balance=PlayerBalance(gold=1000)),
        Player(player_id="p2", app_id=81, table_id="t1", status=PlayerStatus.PENDING.value, enabled=True, balance=PlayerBalance(gold=1000)),
    ])
    # Should not call find_free_player_for_table or start_bot
    await start_scheduled_tables(config)
    find_free_player_mock.assert_not_called()
    start_bot_mock.assert_not_called()

    # Remove one player, now only 1 bot at table
    p2 = await Player.find_one(Player.player_id == "p2")
    p2.status = PlayerStatus.IDLE.value
    p2.table_id = None
    await p2.save()

    find_free_player_mock.return_value = p2

    await start_scheduled_tables(config)
    find_free_player_mock.assert_called()
    start_bot_mock.assert_called()


@pytest.mark.asyncio
async def test_get_matching_tables_occupancy_threshold_logic(mock_db):
    min_players_to_start_new_table = 4
    config = await TablesAutomationConfig(
        config_id=10,
        app_id=81,
        is_enabled=True,
        table_rebuy_threshold=0,
        big_blinds=[100, 200],
        room_modes=[0],
        ante=0,
        players_count=[0, 10],
        currencies=[CurrencyType.DIAMOND],
        start_interval=1,
        stop_interval=1,
        occupancy_threshold=min_players_to_start_new_table
    ).save()

    gaming_configuration = GamingConfiguration(game="Test", blinds=[50, 100, 200], ante=0, game_mode=1, room_mode=0, straddle=False)
    empty_table = await Table(  # noqa: F841
        table_id="empty_table",
        table_name="Table A",
        app_id=81,
        gaming_configuration=gaming_configuration,
        currency="DIAMOND",
        players_total=0,
        empty_seats=69,
    ).save()
    almost_full_table = await Table(
        table_id="almost_full_table",
        table_name="Table B",
        app_id=81,
        gaming_configuration=gaming_configuration,
        currency="DIAMOND",
        players_total=(min_players_to_start_new_table - 1),
        empty_seats=420,
    ).save()

    tables = await get_matching_tables(config)
    assert not tables

    almost_full_table.players_total = min_players_to_start_new_table
    await almost_full_table.save()
    tables = await get_matching_tables(config)
    assert len(tables) == 1
    assert tables[0].table_id == "empty_table"


@pytest.mark.asyncio
async def test_players_with_same_ip(mock_db):
    p1 = Player(player_id="p1", app_id=98, status=PlayerStatus.IDLE.value, enabled=True)
    p2 = Player(player_id="p2", app_id=98, status=PlayerStatus.PLAYING.value, enabled=True)
    p3 = Player(player_id="p3", app_id=98, status=PlayerStatus.IDLE.value, enabled=True)
    p4 = Player(player_id="p4", app_id=98, status=PlayerStatus.PLAYING.value, enabled=True)

    assert await filter_players_using_same_ip([], [p2]) == []
    assert await filter_players_using_same_ip([p1], []) == [p1]

    # No IP pool assigned
    assert await filter_players_using_same_ip([p1], [p2]) == []

    await IPPoolConfiguration(ip_conf_id='c1', assigned_player_ids=['p1'], full_proxy_url='http://proxy1', external_ip='').save()

    assert await filter_players_using_same_ip([p1], [p2, p4]) == [p1]

    await IPPoolConfiguration(ip_conf_id='c2', assigned_player_ids=['p2', 'p3'], full_proxy_url='http://proxy2', external_ip='').save()

    assert await filter_players_using_same_ip([p1, p3], [p2]) == [p1]

    await IPPoolConfiguration(ip_conf_id='c3', assigned_player_ids=['p4'], full_proxy_url='http://proxy3', external_ip='').save()

    # proxy2 is already occupied by p2, so p3 should not be included
    assert await filter_players_using_same_ip([p1, p3], [p2, p4]) == [p1]


@pytest.mark.asyncio
async def test_find_players_for_table_already_playing(mock_db):
    p1 = Player(player_id="p1", app_id=98, status=PlayerStatus.PLAYING.value, table_id='t1', enabled=True)
    p2 = Player(player_id="p2", app_id=98, status=PlayerStatus.PLAYING.value, table_id='t1', enabled=True)
    p3 = Player(player_id="p3", app_id=98, status=PlayerStatus.IDLE.value, enabled=True)

    await IPPoolConfiguration(ip_conf_id='c1', assigned_player_ids=['p1'], full_proxy_url='http://proxy1', external_ip='').save()
    conf2 = await IPPoolConfiguration(ip_conf_id='c2', assigned_player_ids=['p3'], full_proxy_url='http://proxy2', external_ip='').save()

    await Player.insert_many([p1, p2, p3])

    table = Table(
        table_id="t1",
        table_name="Test Table",
        app_id=98,
        gaming_configuration=GamingConfiguration(game="Test", blinds=[0, 0], straddle=False),
        currency="DIAMOND",
        empty_seats=3,
    )

    player = await find_free_player_for_table(table, datetime.now(timezone.utc).time(), 1)
    assert player.player_id == "p3"

    # reassign p3 to the same IP as p1
    await conf2.delete()
    await IPPoolConfiguration(ip_conf_id='c1', assigned_player_ids=['p1', 'p3'], full_proxy_url='http://proxy3', external_ip='').save()

    assert await find_free_player_for_table(table, datetime.now(timezone.utc).time(), 1) is None


@pytest.mark.asyncio
async def test_find_players_for_table_different_app_id(mock_db):
    player = await Player(player_id="p1", app_id=81, allowed_games=[], status=PlayerStatus.IDLE.value, enabled=True).save()

    table = Table(
        table_id="t1",
        table_name="Test Table",
        app_id=85,
        gaming_configuration=GamingConfiguration(game="Test", blinds=[0, 0], straddle=False),
        currency="DIAMOND",
        empty_seats=3,
    )

    assert await find_free_player_for_table(table, datetime.now(timezone.utc).time(), 1) is None

    await player.update(Set({Player.allowed_games: [81, 85, 87]}))

    player = await find_free_player_for_table(table, datetime.now(timezone.utc).time(), 1)
    assert player.player_id == "p1"


@pytest.mark.asyncio
async def test_get_strategy_profile_success():
    """Here we test the case where we already have player with gto"""
    strategies = ["gto", "nit"]
    config = TablesAutomationConfig(
        app_id=81,
        config_id=1,
        is_enabled=True,
        start_interval=5,
        stop_interval=3,
        big_blinds=[100, 200, 320],
        room_modes=[0, 2],
        ante=100,
        currencies=[CurrencyType.DIAMOND],
        strategy_profile={
            strategies[0]: 50,
            strategies[1]: 50,
        },
    )
    profile = get_strategy_profile(config)
    assert profile in strategies


@pytest.mark.asyncio
async def test_get_matching_tables_tables_affected_percentage(mock_db):
    # Insert tables with numeric table_ids
    gc = GamingConfiguration(game="", blinds=[1, 2], ante=0, game_mode=0, room_mode=0, straddle=False)
    await Table.insert_many([
        Table(table_id="100", table_name="T100", app_id=1, gaming_configuration=gc, currency="USD", players_total=5, empty_seats=1),
        Table(table_id="101", table_name="T101", app_id=1, gaming_configuration=gc, currency="USD", players_total=5, empty_seats=1),
        Table(table_id="150", table_name="T150", app_id=1, gaming_configuration=gc, currency="USD", players_total=5, empty_seats=1),
        Table(table_id="199", table_name="T199", app_id=1, gaming_configuration=gc, currency="USD", players_total=5, empty_seats=1),
    ])
    config = await TablesAutomationConfig(
        config_id=1,
        app_id=1,
        is_enabled=True,
        start_interval=1,
        stop_interval=1,
        big_blinds=[2],
        room_modes=[0],
        ante=0,
        players_count=[0, 10],
        currencies=["USD"],
        tables_affected_percentage=50
    ).save()

    tables = await get_matching_tables(config)

    assert {t.table_id for t in tables} == {"100", "101"}


@pytest.mark.asyncio
@patch('src.tasks.tables_automation.BotService.start_bot')
@patch('src.tasks.tables_automation.find_free_player_for_table')
async def test_try_start_bot_zoom_allowed_bots_percent(mock_find_free_player, mock_start_bot, mock_db):
    gc = GamingConfiguration(game="", blinds=[1, 2], ante=0, game_mode=0, room_mode=0, straddle=False)
    table = await Table(table_id="t1", table_name="T1", app_id=87, gaming_configuration=gc, currency="USD", players_total=5, empty_seats=100500).save()

    config = await TablesAutomationConfig(
        config_id=1,
        app_id=87,
        is_enabled=True,
        start_interval=1,
        stop_interval=1,
        big_blinds=[2],
        room_modes=[0],
        ante=0,
        currencies=["USD"],
        max_bots_per_table=1,
        zoom_allowed_bots_percent=35,
    ).save()

    p1 = Player(player_id="p1", app_id=87, status=PlayerStatus.PLAYING.value, table_id='t1', enabled=True)
    p2 = Player(player_id="p2", app_id=87, status=PlayerStatus.PLAYING.value, table_id='t1', enabled=True)
    p3 = Player(player_id="p3", app_id=87, status=PlayerStatus.IDLE.value, enabled=True)

    await Player.insert_many([p1, p2, p3])

    mock_find_free_player.return_value = p3

    await try_start_bot(table, config)
    mock_start_bot.assert_not_called()

    config.zoom_allowed_bots_percent = 41

    await try_start_bot(table, config)
    mock_start_bot.assert_not_called()

    config.max_bots_per_table = 100500

    await try_start_bot(table, config)
    mock_start_bot.assert_called()
