from unittest.mock import AsyncMock, patch
from fastapi.testclient import Test<PERSON>lient
import pytest
from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

from src.utils.enums import PlayerStatus
from src.services.bots import BotService
from src.db.models import GamingConfiguration, Launch, Player, Table
from src.main import app


client = TestClient(app)


@pytest.mark.asyncio
async def test_finish_launch_success(mock_db):
    # Create and save a real Launch object
    fake_created_at = datetime.now(ZoneInfo("UTC")) - timedelta(seconds=120)
    launch = await Launch(
        launch_id="la-123",
        player_id="player-1",
        table_id="table-1",
        app_id=1,
        config_id=1,
        created_at=fake_created_at,
    ).save()

    await BotService.finish_launch("la-123", "some error")

    # Fetch updated launch
    updated = await Launch.find_one(Launch.launch_id == "la-123")
    assert updated.error == "some error"
    assert (updated.updated_at - launch.created_at).total_seconds() > 0


@pytest.mark.asyncio
async def test_finish_launch_not_found(mock_db):
    # Should not raise
    await BotService.finish_launch("not-exist", "err")
    # No assertion needed, just ensure no exception


@pytest.mark.asyncio
@patch("src.services.bots.mq_manager")
async def test_start_bots_api(mock_mq_manager, mock_db):
    mock_mq_manager.add_job = AsyncMock()
    mock_mq_manager.update_job = AsyncMock()
    await Player.insert_many([
        Player(player_id="p1", app_id=81, platform_id=98, status=PlayerStatus.IDLE.value, enabled=True),
        Player(player_id="p2", app_id=81, platform_id=98, status=PlayerStatus.PLAYING.value, enabled=True)
    ])

    gc = GamingConfiguration(game="Test", blinds=[50, 100], straddle=False)
    await Table.insert_many([
        Table(table_id="t1", table_name='t1', app_id=81, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
        Table(table_id="t2", table_name='t2', app_id=81, gaming_configuration=gc, currency="DIAMOND", players_total=1, empty_seats=0),
    ])

    payload = {
        "type": "play",
        "seats": [
            {"player_id": "p1", "table_id": "t1"},
            {"player_id": "p2", "table_id": "t2"},
            {"player_id": "non_existing_player", "table_id": "t2"}
        ]
    }
    response = client.post("/bots/start", json=payload)
    assert response.status_code == 200
    body = response.json()
    assert len(body["data"]) == 3
    print(body)
    assert "p1" in body["data"][0]["bot_id"]
    assert "not found or not idle" in body["data"][1]["error_message"]
    assert "not found or not idle" in body["data"][2]["error_message"]


@pytest.mark.asyncio
@patch("src.services.bots.mq_manager")
async def test_start_bots_api_for_tournament(mock_mq_manager, mock_db):
    mock_mq_manager.add_job = AsyncMock()
    mock_mq_manager.update_job = AsyncMock()
    await Player.insert_many([
        Player(player_id="p1", app_id=83, platform_id=98, status=PlayerStatus.IDLE.value, enabled=True),
    ])

    # No table, will be starting tournament bot

    payload = {
        "type": "play",
        "seats": [
            {"player_id": "p1", "table_id": "tourn1"},
        ]
    }
    response = client.post("/bots/start", json=payload)
    assert response.status_code == 200
    body = response.json()

    assert "p1" in body["data"][0]["bot_id"]
    assert body["data"][0]["bot_id"].startswith("play:98")
    assert body["data"][0]["redis_data"]["tableId"] == "tourn1"


@pytest.mark.asyncio
@patch("src.services.bots.mq_manager")
async def test_start_bots_api_for_friends_new_table(mock_mq_manager, mock_db):
    mock_mq_manager.add_job = AsyncMock()
    mock_mq_manager.update_job = AsyncMock()
    await Player.insert_many([
        Player(player_id="p1", app_id=83, platform_id=98, status=PlayerStatus.IDLE.value, enabled=True),
    ])

    # No table, will be starting tournament bot

    payload = {
        "type": "play",
        "seats": [{
            "player_id": "p1", "table_id": "0",
            "create_room_params": {"name": "Friends Room", "max_players": 6, "currency": "DIAMOND"}
        }]
    }
    response = client.post("/bots/start", json=payload)
    assert response.status_code == 200
    body = response.json()

    assert "p1" in body["data"][0]["bot_id"]
    assert body["data"][0]["bot_id"].startswith("play:98")
    assert body["data"][0]['redis_data']["tableId"] == "0"
    assert body["data"][0]['redis_data']["createRoomParams"] == {"name": "Friends Room", "max_players": 6, "currency": "DIAMOND"}

    assert mock_mq_manager.add_job.call_count == 1
    args, kwargs = mock_mq_manager.add_job.call_args
    assert kwargs["data"]["createRoomParams"] == {"name": "Friends Room", "max_players": 6, "currency": "DIAMOND"}
    assert kwargs["data"]["tableId"] == "0"
