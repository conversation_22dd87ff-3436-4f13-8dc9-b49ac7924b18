from unittest.mock import patch
from src.utils.feature_flags import feature_flags


@patch("src.utils.feature_flags.UnleashClient")
def test_get_flag_local_default(mock_unleash_client):
    # Mock the UnleashClient so no network calls are made
    instance = mock_unleash_client.return_value
    instance.get_feature_toggle.return_value = None
    feature_flags._initialize_client()
    result = feature_flags.get_flag("non_existing_flag", 456)
    assert result == 456
    feature_flags.close()
