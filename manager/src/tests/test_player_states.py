import pytest
from unittest.mock import patch
from src.tasks.player_states import (
    update_player_states,
    get_balance_from_job_progress,
    get_player_updates_for_finished_jobs,
    get_player_update_from_active_job,
    get_player_update_from_completed_job
)
from src.db.models import Player
from src.schemas.responses import PlayerBalanceResponse
from datetime import datetime, timezone
from zoneinfo import ZoneInfo
from src.utils.enums import PlayerStatus, BotType


# Minimal Job class for testing
class Job:
    def __init__(self, job_id="job123", data=None, progress=None):
        self.id = job_id
        self.data = data or {"some": "data"}
        self.progress = progress or {}
        self.failedReason = None

    async def remove(self):
        self.removed = True


def test_get_balance_from_job_progress_returns_balance():
    job = Job()
    job.progress = {
        "data": {
            "balance": {
                "diamond": 10,
                "gold": 20,
                "usdt": 30,
                "usd": 40,
            }
        }
    }
    result = get_balance_from_job_progress(job)
    assert isinstance(result, PlayerBalanceResponse)
    assert result.diamond == 10
    assert result.gold == 20
    assert result.usdt == 30
    assert result.usd == 40


def test_get_balance_from_job_progress_missing_balance_returns_none():
    job = Job()
    job.progress = {"data": {}}
    result = get_balance_from_job_progress(job)
    assert result is None


def test_get_balance_from_job_progress_missing_data_returns_none():
    job = Job()
    job.progress = {}
    result = get_balance_from_job_progress(job)
    assert result is None


def test_get_balance_from_job_progress_balance_partial_fields():
    job = Job()
    job.progress = {
        "data": {
            "balance": {
                "diamond": 5,
                # gold missing
                "usdt": 15,
                # usd missing
            }
        }
    }
    result = get_balance_from_job_progress(job)
    assert isinstance(result, PlayerBalanceResponse)
    assert result.diamond == 5
    assert result.gold == 0
    assert result.usdt == 15
    assert result.usd == 0


def test_get_balance_from_job_progress_balance_is_none():
    job = Job()
    job.progress = {
        "data": {
            "balance": None
        }
    }
    result = get_balance_from_job_progress(job)
    assert result is None


def test_get_balance_from_job_progress_progress_is_zero():
    job = Job()
    job.progress = 0
    result = get_balance_from_job_progress(job)
    assert result is None


@pytest.mark.asyncio
@patch('src.mq.bullmq_manager.mq_manager.get_jobs')
async def test_get_player_updates_for_finished_jobs_balance(get_jobs_mock):
    job = Job(
        job_id='job1',
        data={
            'type': 'balance',
            'playerId': 'p1',
            'launchId': 'l1',
            'tableId': 't1',
            'appId': 1,
            'stopReason': 'done',
        },
        progress={
            'data': {
                'balance': {
                    'diamond': 10,
                    'gold': 20,
                    'usdt': 30,
                    'usd': 40,
                }
            }
        }
    )
    get_jobs_mock.return_value = [job]

    players, launches, future_launches, future_tournament_checks = (
        await get_player_updates_for_finished_jobs()
    )
    assert len(players) == 1
    assert players[0].player_id == 'p1'
    assert players[0].balance.diamond == 10
    assert players[0].balance.gold == 20
    assert players[0].balance.usdt == 30
    assert players[0].balance.usd == 40
    assert len(launches) == 1
    assert launches[0].launch_id == 'l1'
    assert len(future_launches) == 0
    assert len(future_tournament_checks) == 0


@pytest.mark.asyncio
async def test_get_player_update_scan_sets_scanning_status():
    job = Job(
        data={"playerId": "p1", "type": BotType.SCAN.value},
        progress={}
    )
    result = get_player_update_from_active_job(job)
    assert result.player_id == "p1"
    assert result.status == PlayerStatus.SCANNING.value


@pytest.mark.asyncio
async def test_get_player_update_sets_status_and_stats():
    now = int(datetime.now(tz=timezone.utc).timestamp() * 1000)
    job = Job(
        job_id="job1",
        data={"playerId": "p2", "type": BotType.PLAY.value},
        progress={
            "data": {
                "status": "idle",
                "stats": {
                    "handsPlayed": 10,
                    "totalBuyIn": 100,
                    "lastBuyIn": 20,
                    "rebuyCount": 2,
                    "stack": 500,
                    "chips": 300,
                    "rank": 1,
                },
                "updatedAt": now,
            }
        }
    )
    result = get_player_update_from_active_job(job)
    assert result.player_id == "p2"
    assert result.status == PlayerStatus.IDLE.value
    assert result.hands_played == 10
    assert result.total_buy_in == 100
    assert result.last_buy_in == 20
    assert result.rebuy_count == 2
    assert result.stack == 500
    assert result.chips == 300
    assert result.rank == 1
    assert result.updated_at.replace(microsecond=0) == datetime.fromtimestamp(now / 1000, ZoneInfo("UTC")).replace(microsecond=0)
    assert result.bot_id == "job1"


@pytest.mark.asyncio
async def test_get_player_update_no_status():
    job = Job(
        data={"playerId": "p3", "type": BotType.PLAY.value},
        progress={"data": {}}
    )
    result = get_player_update_from_active_job(job)
    assert result is None


@pytest.mark.asyncio
async def test_get_player_update_invalid_status():
    job = Job(
        data={"playerId": "p3", "type": BotType.PLAY.value},
        progress={"data": {"status": "invalid", "stats": {}}}
    )
    result = get_player_update_from_active_job(job)
    assert result is None


@pytest.mark.asyncio
async def test_get_player_update_missing_progress_none():
    job = Job(
        data={"playerId": "p4", "type": BotType.PLAY.value},
        progress=0
    )
    result = get_player_update_from_active_job(job)
    assert result is None


@pytest.mark.asyncio
async def test_get_player_update_status_case_insensitive():
    job = Job(
        data={"playerId": "p6", "type": BotType.PLAY.value},
        progress={"data": {"status": "idle", "stats": {}}}
    )
    result = get_player_update_from_active_job(job)
    assert result.status == PlayerStatus.IDLE.value
    assert result.updated_at is None


def make_completed_job(
    player_id="p1",
    bot_type="balance",
    stop_reason="done",
    failed_reason=None,
    progress=None,
):
    class Job:
        def __init__(self):
            self.data = {
                "playerId": player_id,
                "type": bot_type,
                "stopReason": stop_reason,
            }
            self.failedReason = failed_reason
            self.progress = progress or {}

    return Job()


def test_get_player_update_from_completed_job_balance_sets_balance():
    job = Job(
        data={"playerId": "p1", "type": "balance", "stopReason": "done"},
        progress={
            "data": {
                "balance": {
                    "diamond": 42,
                }
            }
        }
    )

    result = get_player_update_from_completed_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.IDLE.value
    assert result.should_stop is True
    assert result.balance.diamond == 42
    assert result.balance.gold == 0
    assert result.need_balance_update is False


def test_get_player_update_from_completed_job_play_sets_need_balance_update():
    job = Job(
        data={"playerId": "p1", "type": "play"},
        progress={"data": {}}
    )

    result = get_player_update_from_completed_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.IDLE.value
    assert result.need_balance_update is True
    assert result.last_error is None


def test_get_player_update_from_completed_job_transfer_sets_need_balance_update():
    job = Job(
        data={"playerId": "p1", "type": "transfer"},
        progress={"data": {}}
    )

    result = get_player_update_from_completed_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.IDLE.value
    assert result.need_balance_update is True
    assert result.last_error is None


def test_get_player_update_from_completed_job_other_type_sets_defaults():
    job = Job(
        data={"playerId": "p1", "type": "scan"},
        progress={"data": {}}
    )

    result = get_player_update_from_completed_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.IDLE.value
    assert result.need_balance_update is False
    assert result.last_error is None


def test_get_player_update_from_completed_job_invalid_bot_type_raises():
    job = Job(
        data={"playerId": "p1", "type": "not_a_real_type"},
    )
    with pytest.raises(ValueError):
        get_player_update_from_completed_job(job)


def test_get_player_update_from_completed_job_no_progress():
    job = Job(
        data={"playerId": "p1", "type": "play"},
        progress=0
    )

    result = get_player_update_from_completed_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.IDLE.value
    assert result.need_balance_update is True
    assert result.last_error is None


def test_get_player_update_from_active_job_initialized():
    job = Job(
        data={"playerId": "p1", "type": "play"},
        progress={"data": {"status": "initialized"}}
    )

    result = get_player_update_from_active_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.INITIALIZED.value


def test_get_player_update_from_active_job_initialized_with_tables():
    job = Job(
        data={"playerId": "p1", "type": "play"},
        progress={"data": {"status": "initialized", "tables": []}}
    )

    result = get_player_update_from_active_job(job)

    assert result is None


def test_gget_player_update_from_active_job_bought_in():
    job = Job(
        data={"playerId": "p1", "type": "play"},
        progress={"data": {"status": "bought_in", "stats": None}}
    )

    result = get_player_update_from_active_job(job)

    assert result.player_id == "p1"
    assert result.status == PlayerStatus.BOUGHT_IN.value


@pytest.mark.asyncio
@patch("src.tasks.player_states.mq_manager.get_jobs")
@patch("src.tasks.player_states.BotService.stop_bot")
async def test_player_suspended_sets_enabled_false(mock_stop_bot, mock_get_jobs, mock_db):

    player_id = "suspended_player"
    await Player(
        player_id=player_id,
        app_id=1,
        enabled=True,
        status=PlayerStatus.PLAYING.value
    ).save()

    job_data = {
        "playerId": player_id,
        "type": "play",
        "tableId": "t1",
        "appId": -1,
        "stopReason": "Some error happened: Your account has been suspended. Sorry for this :)"
    }
    mock_get_jobs.return_value = [Job(
        "job-suspended",
        job_data,
        {"data": {"status": PlayerStatus.ERROR.value}}
    )]

    await update_player_states()

    updated = await Player.find_one(Player.player_id == player_id)
    assert updated is not None
    assert updated.status == PlayerStatus.SUSPENDED.value
    assert updated.enabled is False
