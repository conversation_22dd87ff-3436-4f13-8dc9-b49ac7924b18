from beanie.operators import In

from src.db.models import FutureLaunch, FutureTournamentCheck, MultiflightConfiguration, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger


async def start_tournaments_checks():
    name = "tasks.start_tournaments_checks"

    busy_players = set()
    processed = False

    unchecked_checks = await FutureTournamentCheck.find(
        FutureTournamentCheck.checked == False,  # noqa: E712
    ).to_list()

    while not processed:
        check = unchecked_checks.pop() if unchecked_checks else None

        if check is None:
            return

        if check.player_id in busy_players:
            logger.info(name, f"Player {check.player_id} is busy, skipping")
            continue

        player = await Player.find(
            Player.status == PlayerStatus.IDLE.value,
            Player.player_id == check.player_id,
        ).first_or_none()
        if not player:
            logger.info(name, f"Player {check.player_id} is not idle")
            busy_players.add(check.player_id)
            continue

        config = await MultiflightConfiguration.find_one(
            In(MultiflightConfiguration.day_one_tournament_ids, [check.tournament_id]),
            MultiflightConfiguration.app_id == check.app_id
        )
        if not config:
            logger.warning(name, f"MultiflightConfiguration not found for tournament_id {check.tournament_id}")
            check.checked = True
            await check.save()
            continue

        if await FutureLaunch.find(
            FutureLaunch.player_id == check.player_id,
            FutureLaunch.tournament_id == config.day_two_tournament_id,
            FutureLaunch.app_id == check.app_id
        ).exists():
            logger.info(name, f"Player {check.player_id} already registered to tournament {config.day_two_tournament_id}")
            check.checked = True
            await check.save()
            continue

        logger.info(name, f"Starting check bot for player_id: {check.player_id} tournament_id:{config.day_two_tournament_id}")
        seat = Seat(table_id=str(config.day_two_tournament_id), player=player)

        try:
            bot_response = await BotService.start_bot(seat=seat, bot_type=BotType.CHECK)
            check.launch_id = bot_response.player.launch_id
        except ValueError as e:
            logger.error(name, f"Failed to start check bot for player {player.player_id}", e)

        check.checked = True
        await check.save()

        processed = True

    logger.info(name, "Deleting checked FutureTournamentChecks")

    await FutureTournamentCheck.find(
        FutureTournamentCheck.checked == True,  # noqa: E712
    ).delete()
