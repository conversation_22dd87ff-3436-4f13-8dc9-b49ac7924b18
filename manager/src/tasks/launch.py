from datetime import datetime
from zoneinfo import ZoneInfo
from beanie.operators import In

from src.db.models import FutureLaunch, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger


async def run_launch_bots():
    name = "tasks.run_launch_bots"

    now = datetime.now(ZoneInfo("UTC"))
    future_launches = await FutureLaunch.find(
        FutureLaunch.starting_time <= now
    ).to_list()

    if not future_launches:
        return

    players = await Player.find(
        In(Player.player_id, [launch.player_id for launch in future_launches])
    ).to_list()

    for future_launch in future_launches:
        player = next((p for p in players if p.player_id == future_launch.player_id), None)

        if not player:
            logger.error(name, f"Player {future_launch.player_id} not found for future launch {future_launch.tournament_id}, delelting future launch")
            await future_launch.delete()
            continue

        if player.status != PlayerStatus.IDLE.value:
            logger.error(name, f"Player {future_launch.player_id} is not idle, skipping future launch {future_launch.tournament_id}")
            continue

        logger.info(name, f"Starting play bot player_id={future_launch.player_id} tournament_id={future_launch.tournament_id}")

        seat = Seat(table_id=future_launch.tournament_id, player=player)

        try:
            await BotService.start_bot(seat=seat, bot_type=BotType.PLAY, is_from_future_launch=True)
            await future_launch.delete()
            return
        except Exception as e:
            logger.error(name, f"Failed to start play bot for player {player.player_id} in tournament {future_launch.tournament_id}", e)
