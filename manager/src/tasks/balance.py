from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

from beanie.operators import NotIn

from src.db.models import FutureLaunch, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger
from src.utils.settings import settings


async def start_balance_bot():
    name = "tasks.start_balance_bot"

    max_booked_launch_time = datetime.now(ZoneInfo("UTC")) + timedelta(hours=settings.no_launch_range_hours)
    future_launches = await FutureLaunch.find(
        FutureLaunch.starting_time <= max_booked_launch_time
    ).to_list()
    reserved_players = [launch.player_id for launch in future_launches]

    player = await Player.find(
        Player.status == PlayerStatus.IDLE.value,
        Player.need_balance_update == True,  # noqa: E712
        NotIn(Player.player_id, reserved_players),
    ).first_or_none()

    if player is None:
        return

    seat = Seat(player=player, table_id=None)
    try:
        await BotService.start_bot(seat=seat, bot_type=BotType.BALANCE, attempts=2)
        logger.info(name, f"Started balance bot for player {player.player_id}")
    except ValueError as e:
        logger.error(name, f"Failed to start balance bot for player {player.player_id}", e)
