from beanie.operators import NotIn, In

from src.db.models import Player, FutureLaunch
from src.mq.bullmq_manager import mq_manager
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import AppType, BotType, PlayerStatus
from src.utils.logging import logger
from src.utils.settings import get_apps, settings


async def start_scanning_bots():
    for club_id in settings.clubs:
        await start_friends_scanning_bot(club_id)

    for app_id in get_apps():
        if app_id == AppType.FRIENDS:
            continue

        await start_scanning_bot(app_id)


async def start_scanning_bot(app_id: AppType):
    name = f'tasks.start_scanning_bot:{app_id}'

    scanning_player = await Player.find(
        Player.bot_type == BotType.SCAN.value,
        Player.app_id == app_id
    ).first_or_none()

    if scanning_player:
        job = await mq_manager.get_job(scanning_player.bot_id)
        if job:
            logger.info(name, "Scanning bot is already running")
            return

    player = await find_player(app_id)
    if player is None:
        logger.warning(name, "No players to scan")
        return

    seat = Seat(table_id=None, player=player)

    logger.info(name, "Starting scanning bot")

    try:
        await BotService.start_bot(
            seat=seat,
            bot_type=BotType.SCAN,
            attempts=1
        )
    except ValueError as e:
        logger.error(name, f"Failed to start scanning bot: {e}")


async def start_friends_scanning_bot(club_id: int):
    name = f'tasks.start_scanning_bot.friends:{club_id}'

    scanning_player = await Player.find(
        In(Player.club_ids, [club_id]),
        Player.bot_type == BotType.SCAN.value,
    ).first_or_none()

    if scanning_player:
        job = await mq_manager.get_job(scanning_player.bot_id)
        if job:
            logger.info(name, "Scanning bot is already running")
            return

    player = await Player.find(
        # not `In(club_ids, [club_id])`, we need to guarantee that the player is in a single club, otherwise he will break other clubs scan bots
        Player.club_ids == [club_id],
        Player.status == PlayerStatus.IDLE.value,
        Player.enabled == True,  # noqa: E712
    ).first_or_none()

    if player is None:
        logger.warning(name, "No players found to scan")
        return

    seat = Seat(table_id=None, player=player, app_id=AppType.FRIENDS, club_id=club_id)

    try:
        await BotService.start_bot(
            seat=seat,
            bot_type=BotType.SCAN,
            attempts=1
        )
        logger.info(name, "Started scanning bot")
    except ValueError as e:
        logger.error(name, f"Failed to start scanning bot: {e}")


async def find_player(app_id: AppType) -> Player | None:
    future_launches = await FutureLaunch.find(
        FutureLaunch.app_id == app_id,
    ).to_list()
    booked_players = [launch.player_id for launch in future_launches]

    match_stage = Player.find(
        Player.app_id == app_id,
        Player.status == PlayerStatus.IDLE.value,
        Player.enabled == True,  # noqa: E712
        NotIn(Player.player_id, booked_players),
    ).get_filter_query()

    pipeline = [
        {"$match": match_stage},
        {"$sample": {"size": 1}}
    ]

    players = await Player.aggregate(pipeline, projection_model=Player).to_list(1)

    return players[0] if players else None
