from dataclasses import dataclass
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from src.services.player_registration_service import PlayerRegistrationService
from src.tasks.launch import run_launch_bots
from src.tasks.overlay_filler import run_overlay_filler
from src.tasks.tables_automation import run_tables_automation
from src.tasks.tasks import touch_lock, check_lock_and_start_scheduler
from src.tasks.tables import update_tables_and_tournaments
from src.tasks.player_states import update_player_states
from src.tasks.scanning import start_scanning_bots
from src.tasks.balance import start_balance_bot
from src.tasks.transfer import start_transfer_bot
from src.tasks.tournaments_check import start_tournaments_checks
from src.tasks.multiflight_check import run_multiflight_check
from src.tasks.check_apps_and_platforms import run_migrations
from src.tasks.check_stuck_players import check_stuck_players
from src.tasks.registration_automation import process_pending_registration
from src.utils.lock import RedisLockException, RedisLock
from src.utils.logging import logger
from src.utils.settings import settings


@dataclass
class Task:
    task_name: str
    task: callable
    trigger: str
    seconds: int
    args: list = None


class Scheduler:
    is_running_jobs = False

    def __init__(self, lock_manager: RedisLock):
        self.lock_key = settings.scheduler_lock_key
        self.classname = "tasks.Scheduler"
        self.scheduler = AsyncIOScheduler()
        self.lock_manager = lock_manager

        # Start a background task to check if the lock is free to a start a proper scheduler
        self.background_task = AsyncIOScheduler()
        if settings.scheduler_enabled:
            logger.info(__class__, "Starting background checker...")
            self.background_task.add_job(
                check_lock_and_start_scheduler,
                "interval",
                seconds=20,
                args=[self.lock_manager, self.lock_key, self],
            )
            self.background_task.start()

    def _add_jobs(self):
        self.scheduler.add_job(run_migrations)
        self.scheduler.add_job(process_pending_registration, "interval", seconds=3, max_instances=1, args=[PlayerRegistrationService()])
        self.scheduler.add_job(update_tables_and_tournaments, "interval", seconds=11, max_instances=1)
        self.scheduler.add_job(update_player_states, "interval", seconds=5, max_instances=1)
        self.scheduler.add_job(start_scanning_bots, "interval", seconds=30, max_instances=1)
        self.scheduler.add_job(start_transfer_bot, "interval", seconds=1, max_instances=1)
        self.scheduler.add_job(start_balance_bot, "interval", seconds=1, max_instances=1)
        self.scheduler.add_job(run_overlay_filler, "interval", seconds=30, max_instances=1)
        self.scheduler.add_job(run_launch_bots, "interval", seconds=5, max_instances=1)
        self.scheduler.add_job(start_tournaments_checks, "interval", seconds=5, max_instances=1)
        self.scheduler.add_job(run_multiflight_check, "interval", seconds=30, max_instances=1)
        self.scheduler.add_job(run_tables_automation, "interval", seconds=30, max_instances=1)
        self.scheduler.add_job(check_stuck_players, "interval", seconds=60, max_instances=1)

    async def start(self):
        try:
            # Create a lock that will expire after 10 seconds
            # To maintain the lock, we need to touch it every 8 seconds
            await self.lock_manager.lock(self.lock_key, expiration=20)

            self.is_running_jobs = True

            self.scheduler.add_job(
                touch_lock,
                "interval",
                seconds=10,
                args=[self.lock_manager, self.lock_key],
            )

            self._add_jobs()

            self.scheduler.start()
            logger.info(self.classname, "Scheduler started.")
        except RedisLockException:
            logger.warning(self.classname, "Scheduler was already started somewhere else.")
        except Exception as e:
            logger.error(self.classname, "Error starting scheduler.", str(e))

    def stop_background_task(self):
        self.background_task.shutdown()

    async def stop(self):
        logger.info(self.classname, "Stopping scheduler...")
        await self.lock_manager.release(self.lock_key)
        self.scheduler.shutdown()
