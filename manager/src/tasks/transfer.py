from datetime import datetime, timedelta
from zoneinfo import ZoneInfo

from beanie.operators import NotIn

from src.db.models import FutureLaunch, TaskLaunch, Player
from src.schemas.requests import Seat
from src.services.bots import BotService
from src.utils.enums import BotType, PlayerStatus
from src.utils.logging import logger
from src.utils.settings import settings


async def start_transfer_bot():
    name = "tasks.start_transfer_bot"

    max_booked_launch_time = datetime.now(ZoneInfo("UTC")) + timedelta(hours=settings.no_launch_range_hours)
    future_launches = await FutureLaunch.find(
        FutureLaunch.starting_time <= max_booked_launch_time
    ).to_list()
    reserved_players = [launch.player_id for launch in future_launches]

    task_launch = await TaskLaunch.find(
        NotIn(TaskLaunch.player_id, reserved_players)
    ).first_or_none()
    if task_launch is None:
        return

    player = await Player.find_one(
        Player.player_id == task_launch.player_id,
        Player.status == PlayerStatus.IDLE,
    )
    if player is None:
        logger.warning(name, f"Player {task_launch.player_id} not found, removing transfer bot task")
        await task_launch.delete()
        return

    if not player.enabled:
        logger.warning(name, f"Player {task_launch.player_id} is disabled, removing transfer bot task")
        await task_launch.delete()
        return

    seat = Seat(
        player=player,
        table_id=None,
        transfer_amount=task_launch.transfer_amount,
        receiver_id=task_launch.receiver_id,
        receiver_username=task_launch.receiver_username,
        currency=task_launch.currency,
    )

    try:
        await BotService.start_bot(seat=seat, bot_type=BotType.TRANSFER, attempts=2)
        logger.info(name, f"Transfer bot started for player {task_launch.player_id}")
        await task_launch.delete()
    except ValueError as e:
        logger.error(name, f"Failed to start transfer bot for player {task_launch.player_id}", e)
