from typing import List, Optional
from fastapi import Query

from src.utils.settings import get_apps, get_platforms, settings
from src.routers.base_router import BaseRouter
from src.schemas.requests import TournamentConfigUpdateRequest, TournamentsConfigUpdateRequest
from src.schemas.responses import (
    AppResponse,
    PlatformResponse,
    Response,
    TableResponse,
    TournamentDetailsResponse,
    TournamentResponse,
)
from src.services.tables import TableService
from src.services.tournaments import TournamentService


router = BaseRouter()


@router.get("/table")
async def list_tables(
    app_id: int,
    club_id: Optional[int] = Query(None),
    straddle: bool = Query(None, description="Filter tables by straddle"),
    ante: bool = Query(None, description="Filter tables by ante"),
) -> Response[List[TableResponse]]:
    tables = await TableService.get_tables(app_id, club_id, straddle, ante)
    return tables


@router.get("/app")
async def list_apps() -> Response[List[AppResponse]]:
    apps = get_apps()
    return [AppResponse(app_id=app, name=app.name) for app in set(apps)]


@router.get("/platforms/")
async def list_platforms() -> Response[List[PlatformResponse]]:
    return [PlatformResponse(platform_id=platform, name=platform.name) for platform in get_platforms()]


@router.get("/clubs")
async def get_clubs():
    return Response(code=200, message="OK", data={"clubs": settings.clubs})


@router.get("/health")
async def health_check():
    return {"status": "ok"}


@router.put("/tournaments")
async def update_tournaments_details(request: TournamentsConfigUpdateRequest):
    await TournamentService.update_tournament_configurations(
        app_id=request.app_id,
        tournament_ids=request.tournament_ids,
        adjusted_game_pool=request.adjusted_game_pool,
        adjusted_game_pool_updated_manually=True,
        scheduling_min_delay_sec=request.scheduling_min_delay_sec,
        scheduling_max_delay_sec=request.scheduling_max_delay_sec,
    )


@router.get("/tournaments")
async def list_tournaments(app_id: int) -> Response[List[TournamentResponse]]:
    tournaments = await TournamentService.get_tournaments(app_id)
    return tournaments


@router.get("/tournaments/{tournament_id}/")
async def get_tournament_details(tournament_id: str, app_id: int) -> Response[TournamentDetailsResponse]:
    tournament = await TournamentService.get_tournament_details(app_id, tournament_id)
    return tournament


@router.post("/tournaments/{tournament_id}/")
async def update_tournament_details(request: TournamentConfigUpdateRequest, tournament_id: str) -> Response[TournamentResponse]:
    await TournamentService.update_tournament_configurations(
        app_id=request.app_id,
        tournament_ids=[tournament_id],
        adjusted_game_pool=request.adjusted_game_pool,
        adjusted_game_pool_updated_manually=True,
        scheduling_min_delay_sec=request.scheduling_min_delay_sec,
        scheduling_max_delay_sec=request.scheduling_max_delay_sec,
    )

    tournament_response = await TournamentService.get_tournament_by_id(
        app_id=request.app_id, tournament_id=tournament_id
    )
    return tournament_response
