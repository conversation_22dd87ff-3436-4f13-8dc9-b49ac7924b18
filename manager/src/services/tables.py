from datetime import datetime
from zoneinfo import ZoneInfo
from dataclasses import dataclass
from typing import List, TypedDict
from beanie.operators import In

from src.db.models import Table, GamingConfiguration, Player
from src.schemas.responses import TableResponse
from src.utils.enums import PlayerStatus


class IncomingTableDataDict(TypedDict):
    tableId: str
    tableName: str
    leftSeats: int
    gameType: str
    blinds: List[int]
    ante: int
    gameMode: int
    roomMode: int
    straddle: bool
    currency: str
    maxPlayers: int
    playersCount: int
    appId: int


@dataclass
class TableUpdate():
    table_id: str
    table_name: str
    app_id: int
    empty_seats: int
    max_players: int
    game: str
    blinds: List[int]
    ante: bool
    game_mode: int
    room_mode: int
    straddle: bool
    currency: str
    players_count: int

    @staticmethod
    def from_worker_data(worker_data: IncomingTableDataDict) -> "TableUpdate":
        return TableUpdate(
            table_id=str(worker_data["tableId"]),
            table_name=worker_data["tableName"],
            app_id=worker_data["appId"],
            empty_seats=int(worker_data["leftSeats"]),
            game=worker_data["gameType"],
            blinds=worker_data["blinds"],
            ante=worker_data["ante"],
            game_mode=worker_data["gameMode"],
            room_mode=worker_data["roomMode"],
            straddle=worker_data["straddle"],
            currency=worker_data["currency"],
            max_players=int(worker_data["maxPlayers"]),
            players_count=int(worker_data["playersCount"]),
        )


class TableService:
    @staticmethod
    async def get_tables(
        app_id: int, club_id: int = None, straddle: bool = None, ante: bool = None
    ) -> list[TableResponse]:

        query = [Table.app_id == app_id]

        if club_id is not None:
            query.append(Table.club_id == club_id)

        if straddle is not None:
            query.append(Table.gaming_configuration.straddle == straddle)
        if ante is not None:
            query.append(Table.gaming_configuration.ante > 0 if ante else Table.gaming_configuration.ante == 0)

        db_tables = await Table.find(*query).to_list()
        db_active_table_players = await Player.find(
            In(Player.table_id, [table.table_id for table in db_tables]),
            Player.status != PlayerStatus.IDLE.value
        ).to_list()

        table_responses = []
        for table in db_tables:
            table_players = [player for player in db_active_table_players if player.table_id == table.table_id]
            table_responses.append(table.to_response(table_players))

        return table_responses

    @staticmethod
    async def drop_and_insert_tables(tables_updates: List[TableUpdate], club_id: int = None):
        """
        We expect that tables_updates contain all the tables for corresponding app_ids or club_ids
        """
        updated_app_ids = [table_update.app_id for table_update in tables_updates]
        filters = [In(Table.app_id, updated_app_ids)]

        if club_id:
            # not `append` because then for empty list of tables we won't clean up the db
            filters = [Table.club_id == club_id]

        await Table.find(*filters).delete()

        tables = []
        for table_update in tables_updates:
            gc = GamingConfiguration(
                game=table_update.game,
                blinds=table_update.blinds,
                ante=table_update.ante,
                game_mode=table_update.game_mode,
                room_mode=table_update.room_mode,
                straddle=table_update.straddle,
            )
            table = Table(
                table_id=table_update.table_id,
                table_name=table_update.table_name,
                app_id=table_update.app_id,
                club_id=club_id,
                gaming_configuration=gc,
                currency=table_update.currency,
                empty_seats=table_update.empty_seats,
                players_total=table_update.players_count,
                date_updated=datetime.now(ZoneInfo("UTC"))
            )
            tables.append(table)

        if tables:
            await Table.insert_many(tables)
