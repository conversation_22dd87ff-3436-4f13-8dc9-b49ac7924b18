from datetime import datetime, time
from typing import List, Generic, TypeVar, Optional, Dict
from pydantic import BaseModel, ConfigDict

from src.utils.enums import AppType, PokerGame, PlatformType, StrategyProfile

T = TypeVar("T")


class Response(BaseModel, Generic[T]):
    code: int
    message: str
    data: T


class AppResponse(BaseModel):
    app_id: AppType
    name: str

    model_config = ConfigDict(
        use_enum_values=True,
    )


class PlatformResponse(BaseModel):
    platform_id: PlatformType
    name: str

    model_config = ConfigDict(
        use_enum_values=True,
    )


class GamingConfigurationResponse(BaseModel):
    game: PokerGame
    blinds: List[float]
    ante: int
    game_mode: int
    room_mode: int
    straddle: bool


class MultiflightConfigurationResponse(BaseModel):
    multi_flight_id: int
    day_one_tournament_ids: List[str]
    day_two_tournament_id: str
    date_updated: datetime
    app_id: int


class TicketResponse(BaseModel):
    ticket_id: str = "0"
    amount: int = 0


class PlayerBalanceResponse(BaseModel):
    diamond: Optional[float] = 0
    gold: Optional[float] = 0
    usdt: Optional[float] = 0
    usd: Optional[float] = 0
    tickets: Optional[List[TicketResponse]] = [
        TicketResponse(),
    ]


class PlayTimeRangeResponse(BaseModel):
    start: time
    end: time


class PlayerResponse(BaseModel):
    player_id: str
    app_id: int
    allowed_games: List[AppType]
    platform_id: PlatformType
    country_code: str
    enabled: bool
    status: str
    should_stop: Optional[bool] = False
    bot_id: Optional[str] = None
    bot_type: Optional[str] = None
    table_id: Optional[str] = None
    last_error: Optional[str] = None
    need_balance_update: Optional[bool] = False

    avatar_url: Optional[str] = None
    avatar_changed: Optional[bool] = False

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    launch_id: Optional[str] = None

    hands_played: int
    total_buy_in: Optional[int] = 0
    last_buy_in: Optional[int] = 0
    rebuy_count: Optional[int] = 0
    stack: Optional[float] = 0

    chips: Optional[float] = 0
    rank: Optional[int] = 0

    balance: PlayerBalanceResponse
    ticket_id: Optional[str] = None
    play_time_range: PlayTimeRangeResponse
    strategy_profile: Optional[str] = None

    model_config = ConfigDict(
        use_enum_values=True,
    )


class PlayerResponseExtended(PlayerResponse):
    ip_conf_id: Optional[str] = None
    external_ip: Optional[str] = None


class PlayerShortResponse(BaseModel):
    player_id: str
    status: str
    rank: int
    chips: float


class PlayerStatusResponse(BaseModel):
    player_id: str
    status: str


class PlayerTableResponse(BaseModel):
    player_id: str
    bot_id: str
    status: str


class TableResponse(BaseModel):
    table_id: str
    table_name: str
    app_id: int
    gaming_configuration: GamingConfigurationResponse
    currency: str
    bots: List[PlayerTableResponse]
    players_total: int
    empty_seats: int


class BotResponse(BaseModel):
    redis_data: Optional[dict] = None
    bot_id: str
    player: Optional[PlayerResponse] = None


class ErrorResponse(BaseModel):
    error_message: str
    exception_string: Optional[str] = None


class RegisterPlayerResponse(BaseModel):
    player_id: Optional[str] = None
    app_id: Optional[int] = None
    platform_id: Optional[int] = None
    user_id: Optional[str] = None
    username: Optional[str] = None
    password: Optional[str] = None
    account: Optional[str] = None
    country_code: str
    area_code: Optional[str] = None
    phone_number: Optional[str] = None
    email: Optional[str] = None
    external_ip: Optional[str] = None
    error: Optional[str] = None
    exception: Optional[str] = None
    response: Optional[str] = None


class PendingRegistrationResponse(BaseModel):
    pending_registration_id: str
    username: Optional[str]
    account: Optional[str]
    password: Optional[str]
    country_code: str
    area_code: Optional[str]
    phone_number: Optional[str]
    receiver_id: Optional[int]
    receiver_username: Optional[str]
    email: Optional[str]
    app_id: int
    platform_id: int
    updated_at: datetime
    error: Optional[str]
    status: str
    player_id: Optional[str]
    user_id: Optional[str]

    model_config = {
        "extra": "allow",
    }


class AddUserAccountResponse(BaseModel):
    player_id: Optional[str] = None
    platform_id: Optional[int] = None
    country_code: str
    phone_number: Optional[str] = None
    account: Optional[str] = None
    external_ip: Optional[str] = None
    error: Optional[str] = None
    exception: Optional[str] = None


class TournamentResponse(BaseModel):
    tournament_id: str
    tournament_name: str
    tournament_name_eng: Optional[str] = None
    starting_time: datetime
    seats_per_table: int
    registration_fee: float
    service_fee: float = 0
    game_pool: float = 0
    overlay: float = 0
    currency: str
    registered_count: int = 0
    joined_count: int = 0
    status: Optional[int] = None
    mtt_mode: Optional[int] = None
    tournament_mode: Optional[int] = None
    game_mode: Optional[int] = None
    is_satellite_mode: bool = False
    multi_flight_id: Optional[int] = None
    multi_flight_level: Optional[int] = None
    sign_up_options: Optional[str] = None
    app_id: int
    date_updated: datetime
    late_registration_time: Optional[datetime] = None

    # In case we have some specific value for game_pool
    adjusted_game_pool: float = 0
    adjusted_game_pool_updated_manually: bool = False
    scheduling_min_delay_sec: Optional[int] = None
    scheduling_max_delay_sec: Optional[int] = None

    highest_rank: int = 0
    highest_chips: float = 0
    amount_of_players: int = 0
    bots_pending: int = 0
    bots_started: int = 0
    overlay_percentage: float = 0


class LaunchResponse(BaseModel):
    launch_id: str
    player_id: str
    table_id: str
    app_id: int
    config_id: Optional[int] = None
    error: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    finished: Optional[bool] = None


class AutoStartActionResponse(BaseModel):
    app_id: int
    tournament_id: str
    number_of_players: int
    started: datetime
    finished: datetime


class AutoStartConfigResponse(BaseModel):
    app_id: int
    enabled: bool
    cover_before_start: float
    cover_late_registration: float
    bot_min_delay_sec: int
    bot_max_delay_sec: int
    schedule_min_players: int
    check_interval_sec: int
    check_before_start_min: int
    min_prize_pool: int


class TournamentDetailsResponse(BaseModel):
    players: List[PlayerShortResponse]
    tournament_id: str
    actions: List[AutoStartActionResponse]


class FutureLaunchResponse(BaseModel):
    player_id: str
    tournament_id: str
    app_id: int
    ticket_id: Optional[str] = None
    starting_time: Optional[datetime] = None


class TournamentCheckResponse(BaseModel):
    player_id: str
    tournament_id: str
    app_id: int
    checked: bool
    day_2_tournament_id: Optional[str] = None
    status: Optional[str] = None


class IPPoolConfigurationResponse(BaseModel):
    ip_conf_id: Optional[str] = None
    full_proxy_url: Optional[str] = None
    external_ip: Optional[str] = None
    country_code: Optional[str] = None
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    assigned_player_ids: List[str] = []


class TablesAutomationConfigResponse(BaseModel):
    config_id: int
    app_id: int
    is_enabled: bool
    start_interval: int
    start_last_update: datetime
    stop_interval: int
    stop_last_update: datetime
    created_at: datetime
    big_blinds: List[int]
    table_rebuy_threshold: int
    room_modes: List[int]
    players_count: List[int]
    min_players_count_to_stop: int
    max_players_count_to_stop: int
    ante: int
    currencies: List[str]
    max_bots_per_table: int
    max_rebuy_count: int
    buy_in_multipliers: List[int]
    occupancy_threshold: int
    strategy_profile: Dict[StrategyProfile, int]
    tables_affected_percentage: int
    zoom_allowed_bots_percent: int

    withdraw_amount: int
    withdraw_threshold: int


class RegistrationAutomationConfigResponse(BaseModel):
    config_id: int
    is_enabled: bool

    min_delay_sec: int
    max_delay_sec: int

    last_registration_time: datetime | None
    next_registration_time: datetime | None
