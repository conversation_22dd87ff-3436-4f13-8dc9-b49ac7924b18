from typing import List
from bullmq import Queue
from bullmq.job import Job
from bullmq.types import JobOptions, BackoffOptions
from src.utils.settings import settings
from src.mq.redis_cluster import HackedRedisCluster
from src.utils.logging import logger

from redis.asyncio import Redis


class BullMQException(Exception):
    pass


class BullMQManager:
    def __init__(self, queue_name):
        self.classname = "mq.BullMQManager"
        logger.info(self.classname, f"Initializing BullMQManager with queue {queue_name}")
        if settings.env == "local":
            if settings.local_explicit_redis:
                redis_conn = Redis(host=settings.redis_host, port=settings.redis_port, db=0)
                self.queue = Queue(queue_name,  {"connection": redis_conn})
            else:
                self.queue = Queue(queue_name)
        else:
            redis_conn = HackedRedisCluster(host=settings.redis_host, port=settings.redis_port, db=0)
            self.queue = Queue(
                queue_name,
                {"connection": redis_conn},
            )
        self.last_job_id = None

    async def add_job(self, data: dict, job_id: str, job_type: str, delay: int, attempts: int):
        existing_job = await Job.fromId(self.queue, job_id)
        if existing_job:
            exception_str = f"Job {existing_job.id} already exists"
            logger.error(self.classname, exception_str)
            raise BullMQException(exception_str)

        options = JobOptions(jobId=job_id, delay=int(delay * 1000), attempts=attempts, backoff=BackoffOptions(type="exponential", delay=5000))
        job = await self.queue.add(job_type, data, options)
        logger.info(self.classname, f"Added job {job.id}")

    async def remove_job(self, job_id: str) -> None:
        job: Job = await Job.fromId(self.queue, job_id)
        if not job:
            raise BullMQException(f"Job {job_id} does not exist")

        await job.remove()
        logger.info(self.classname, f"Removed job {job.id}")

    async def update_job(self, bot_id: str, data: dict) -> str:
        job = await Job.fromId(self.queue, bot_id)
        if job is None:
            raise BullMQException(f"Job {bot_id} does not exist")

        updated_data = job.data

        for key, value in data.items():
            updated_data[key] = value

        if job:
            await job.updateData(updated_data)
            logger.info(self.classname, f"Updated job {job.id} with {data=}")
        return job.id

    async def get_jobs(self, states: List[str] = []) -> List[Job]:
        try:
            jobs = await self.queue.getJobs(states)
            return jobs
        except ValueError:
            return []

    async def get_job(self, job_id: str) -> Job:
        job: Job = await Job.fromId(self.queue, job_id)
        return job

    async def update_progress(self, bot_id: str, progress: int) -> str:
        job: Job = await Job.fromId(self.queue, bot_id)
        await job.updateProgress({"progress": progress})
        return job.id


mq_manager = BullMQManager("{worker}")
