from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError

from src.routers.router import router as main_router
from src.routers.bots import bot_router
from src.routers.authentication import auth_router
from src.routers.players import player_router
from src.routers.automation import automation_router
from src.routers.multiflight import multiflight_router
from src.routers.ip_pool import ip_pool_router
from src.utils.settings import settings
from src.utils.exception_handlers import validation_exception_handler
from src.tasks.scheduler import Scheduler
from src.utils.logging import logger, setup_uvicorn_logging
from src.db.connection import init_db
from src.utils.lock import RedisLock
from src.utils.feature_flags import feature_flags


@asynccontextmanager
async def lifespan(app: FastAPI):
    # This function will be called when the application starts and stops
    # Before the yield, you can add code that runs on startup
    name = "lifespan"
    logger.info(name, "Application started")
    db_client = await init_db(settings)
    scheduler = Scheduler(RedisLock())
    feature_flags._initialize_client()
    setup_uvicorn_logging()

    yield

    # After the yield, you can add code that runs on shutdown
    logger.info(name, "Application is stopping...")
    scheduler.stop_background_task()
    if scheduler.is_running_jobs:
        await scheduler.stop()
    db_client.close()
    feature_flags.close()


app = FastAPI(
    root_path="/api",
    openapi_url="/openapi.json",
    docs_url="/docs",
    title="Manager API",
    description="API for managing some secret entities",
    version="0.1",
    lifespan=lifespan,
)

# Register routes
app.include_router(main_router)
app.include_router(player_router)
app.include_router(bot_router)
app.include_router(auth_router)
app.include_router(automation_router)
app.include_router(multiflight_router)
app.include_router(ip_pool_router)

# Register exception handler
app.add_exception_handler(RequestValidationError, validation_exception_handler)


app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_hosts,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
