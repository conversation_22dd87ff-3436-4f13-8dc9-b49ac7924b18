from enum import Enum


class StrategyProfile(str, Enum):
    GTO = "gto"
    LOOSE_PASSIVE1 = "loose-passive1"
    LOOSE_PASSIVE2 = "loose-passive2"
    TIGHT_AGGRESSIVE = "tight-aggressive"
    NIT = "nit"
    LOOSE_AGGRESSIVE = "loose-aggressive"
    TIGHT_PASSIVE = "tight-passive"
    PURE_CALL = "pure-call"
    HYPER_AGGRESSIVE = "hyper-aggressive"
    PLAY_EVERYTHING = "play-everything"


class PlatformType(Enum):
    WPK = 98
    WPTGO = 101
    RWPK = 94  # Deprecated


class TournamentStatus(Enum):
    Registering = 0
    LateRegistration = 1
    Running = 2


class AppType(Enum):
    FRIENDS = 69
    R1 = 81  # Cash
    R2 = 82  # Diamond
    R3 = 83  # Tournament
    R4 = 84  # Tournament (deprecated)
    R5 = 85  # Splash
    R6 = 86  # Splash diamond
    R7 = 87  # Zoom
    R9 = 89  # Shortdeck


class PokerGame(Enum):
    NLHE = "NLHE"


class PlayerStatus(Enum):
    # Combination of worker PlayerStatus enum and pwk UserStatus enum
    IDLE = "IDLE"
    PENDING = "PENDING"  # Got request to start the bot, but bot is not started yet
    INITIALIZED = "INITIALIZED"  # Bot is started, but not yet logged in
    LOGGED_IN = "LOGGED_IN"  # Bot is logged in, but not yet joined the table
    SCANNING = "SCANNING"
    WAITING = "WAITING"  # Tournament is not started yet
    IN_ROOM = "IN_ROOM"
    JOINED = "JOINED"  # Bot is joined the table
    BOUGHT_IN = "BOUGHT_IN"  # Bot is bought in
    SAT_DOWN = "SAT_DOWN"  # Bot is sat down
    SAT_OUT = "SAT_OUT"
    PLAYING = "PLAYING"  # Bot is actually playing
    STUCK = "STUCK"  # Bot is stuck after all in and does not now what to do
    ERROR = "ERROR"  # Bot is in error state
    VIEWING_TIME_EXPIRED = "VIEWING_TIME_EXPIRED"  # Bot is in a special error state when it's viewing time of the table is expired

    STOPPING = "STOPPING"  # Got request to stop the bot, but bot is not stopped yet
    STOPPED = "STOPPED"  # Bot is stopped
    SUSPENDED = "SUSPENDED"


class RegistrationStatus(Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    FAILED = "FAILED"
    SUCCESS = "SUCCESS"


class BotType(Enum):
    PLAY = "play"
    SCAN = "scan"
    BALANCE = "balance"
    CHECK = "check"
    TRANSFER = "transfer"


class FutureTournamentCheckStatus(Enum):
    ELIGIBLE = "ELIGIBLE"
    NOT_ELIGIBLE = "NOT ELIGIBLE"
    NOT_AVAILABLE = "NOT AVAILABLE"


class CurrencyType(str, Enum):
    USD = "USD"
    GOLD = "GOLD"
    DIAMOND = "DIAMOND"
