from typing import List
from pydantic_settings import BaseSettings
from pydantic import Field, ValidationError
from src.utils.enums import PlatformType, AppType

from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    db_name: str = "manager"

    env: str = Field("dev", alias="ENV")

    mongo_host: str = Field("localhost", alias="MONGO_HOST")
    mongo_port: int = Field(27017, alias="MONGO_PORT")
    mongo_user: str = Field("", alias="MONGO_USER")
    mongo_password: str = Field("", alias="MONGO_PASSWORD")
    mongo_ssl_cert: str = Field("", alias="MONGO_SSL_CERT")
    mongo_ssl_disabled: bool = Field(False, alias="MONGO_SSL_DISABLED")

    redis_host: str = Field("localhost", alias="REDIS_HOST")
    redis_port: int = Field(6379, alias="REDIS_PORT")

    allowed_hosts: list = Field(["*"], alias="ALLOWED_HOSTS")

    secret_key: str = Field(
        "ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg=", alias="SECRET_KEY"
    )
    secret_access_key: str = Field("", alias="SECRET_ACCESS_KEY")

    scheduler_lock_key: str = Field("scheduler", alias="SCHEDULER_LOCK_KEY")
    scheduler_enabled: bool = Field(True, alias="SCHEDULER_ENABLED")

    wpk_sign_salt: str = Field("WAv1yNGx", alias="WPK_SIGN_SALT")
    wpk_url: str = Field("http://**************", alias="WPK_URL")
    wptgo_url: str = Field("http://***********:7170", alias="WPTGO_URL")
    wptgo_token: str = Field("", alias="WPTGO_TOKEN")
    wptgo_app_bundle_id: str = Field("com.wptasia.wpt", alias="WPTGO_APP_BUNDLE_ID")

    worker_url: str = Field("http://localhost:3000", alias="WORKER_URL")

    no_launch_range_hours: int = Field(10)

    auth_disabled: bool = Field(True, alias="AUTH_DISABLED")
    cognito_region: str = Field("", alias="COGNITO_REGION")
    cognito_user_pool_id: str = Field("", alias="COGNITO_USER_POOL_ID")
    cognito_client_id: str = Field("", alias="COGNITO_CLIENT_ID")
    cognito_client_secret: str = Field("", alias="COGNITO_CLIENT_SECRET")

    local_explicit_redis: bool = Field(False, alias="LOCAL_EXPLICIT_REDIS")

    ip_pool_enabled: bool = Field(True, alias="IP_POOL_ENABLED")
    max_players_per_ip: int = Field(1, alias="MAX_PLAYERS_PER_IP")

    job_stall_timeout: int = Field(3600, alias="JOB_STALL_TIMEOUT")

    enable_feature_flags: bool = Field(True, alias="ENABLE_FEATURE_FLAGS")
    unleash_api_url: str = Field("https://unleash.dev.fungamer.io/api", alias="UNLEASH_API_URL")
    unleash_api_token: str = Field("", alias="UNLEASH_API_TOKEN")

    disable_ip_pool_app_ids_check: bool = Field(False, alias="DISABLE_IP_POOL_APP_IDS_CHECK")

    platforms: dict = Field(
        default_factory=lambda: {
            94: {"name": "RWPK", "games": [84]},
            98: {"name": "WPK", "games": [69, 81, 82, 83, 85, 86, 87, 89]},
            101: {"name": "WPTGO", "games": [81, 83, 85, 87, 89]},
        },
        alias="PLATFORMS",
    )

    # includes dev env users a5team7, a5team18, a5team10, a5team5, a5team8
    clubs: list[int] = Field(default=[], alias="CLUBS")


settings = Settings()


def get_platforms() -> List[PlatformType]:
    return [PlatformType(int(platform)) for platform in settings.platforms.keys()]


def get_apps() -> List[AppType]:
    return [AppType(game) for platform in settings.platforms.values() for game in platform['games']]


def validate_platform_keys_and_games():
    for key, platform in settings.platforms.items():
        if int(key) not in PlatformType:
            raise ValidationError(f"Platform key '{key}' is not of type PlatformType ({PlatformType})")
        print(f"Validating platform {key} with games {platform.get('games')}")
        for game in platform.get("games"):
            if game not in AppType:
                raise ValidationError(f"Game '{game}' in platform '{key}' is not of type AppType ({AppType})")


validate_platform_keys_and_games()
